import { describe, expect, it, vi } from "vitest";
process.env.SKIP_ENV_VALIDATION = "true";
vi.mock("@/server/auth", () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import("../../trpc");
const { movementRouter } = await import("../movement");
const createCaller = createCallerFactory(movementRouter);
vi.mock("xlsx", () => ({
	read: vi.fn(),
	utils: { sheet_to_json: vi.fn() },
}));

const baseCtx = {
	db: {},
	logger: undefined,
	headers: new Headers(),
} as unknown as Record<string, unknown>;

describe("movementRouter.validate", () => {
	it("detects missing amount", async () => {
		const dbMock = {
			salaryTransaction: { findFirst: vi.fn().mockResolvedValue(null) },
		} as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1", tenantId: "t1" } },
                });
		const res = await caller.validate({
			employeeId: "e1",
			periodMonth: 5,
			periodYear: 2024,
		});
		expect(res.isValid).toBe(false);
		expect(res.errors).toContain("נדרש סכום או שיעור");
	});

        it("detects overlap", async () => {
                const dbMock = {
                        salaryTransaction: { findFirst: vi.fn().mockResolvedValue({ id: "t1" }) },
                } as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1" } },
                });
                const res = await caller.validate({
                        employeeId: "e1",
                        periodMonth: 5,
                        periodYear: 2024,
                        amount: 100,
                        fromDate: new Date("2024-05-01"),
                        toDate: new Date("2024-05-05"),
                });
                expect(res.isValid).toBe(false);
                expect(res.errors).toContain("קיימת תנועה חופפת");
        });
});

describe("movementRouter.create", () => {
        it("uses tenantId from context", async () => {
                const dbMock = {
                        user: { findUnique: vi.fn().mockResolvedValue({ tenantId: "t1" }) },
                        salaryTransaction: {
                                findFirst: vi.fn().mockResolvedValue(null),
                                create: vi.fn().mockResolvedValue({ id: "tx1" }),
                        },
                } as any;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1" } },
                        tenantId: "t1",
                });
                await caller.create({
                        employeeId: "e1",
                        periodMonth: 5,
                        periodYear: 2024,
                        amount: 100,
                });
                expect(dbMock.salaryTransaction.create).toHaveBeenCalledWith({
                        data: {
                                employeeId: "e1",
                                periodMonth: 5,
                                periodYear: 2024,
                                amount: 100,
                                tenantId: "t1",
                        },
                });
        });
});

describe("movementRouter.importExcel", () => {
	it("imports valid rows", async () => {
		const xlsx = vi.mocked(await import("xlsx"));
		xlsx.read.mockReturnValue({
			SheetNames: ["Sheet1"],
			Sheets: { Sheet1: {} },
		});
		xlsx.utils.sheet_to_json.mockReturnValue([
			{
				employeeId: "e1",
				periodMonth: "5",
				periodYear: "2024",
				amount: "100",
			},
		]);
		const dbMock = {
			salaryTransaction: {
				create: vi.fn().mockResolvedValue({}),
				findFirst: vi.fn().mockResolvedValue(null),
			},
		} as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1", tenantId: "t1" } },
                });
		const res = await caller.validate({
			employeeId: "e1",
			periodMonth: 5,
			periodYear: 2024,
			amount: 100,
			fromDate: new Date("2024-05-01"),
			toDate: new Date("2024-05-05"),

		});
		xlsx.utils.sheet_to_json.mockReturnValue([
			{
				periodMonth: "5",
				periodYear: "2024",
			},
		]);
		const dbMock = {
			salaryTransaction: {
				create: vi.fn().mockResolvedValue({}),
				findFirst: vi.fn().mockResolvedValue(null),
			},
		} as unknown as Record<string, unknown>;
		const caller = createCaller({
			...baseCtx,
			db: dbMock,
			session: { user: { id: "u1" } },
		});
		const res = await caller.importExcel("file");
		expect(dbMock.salaryTransaction.create).not.toHaveBeenCalled();
		expect(res).toEqual({ imported: 0, total: 1 });
	});
});

describe('movementRouter.create', () => {
  it('uses tenant from session', async () => {
    const dbMock = {
      salaryTransaction: {
        findFirst: vi.fn().mockResolvedValue(null),
        create: vi.fn().mockResolvedValue({ id: 'tx1' }),
      },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', tenantId: 't1' } },
    });
    await caller.create({
      employeeId: 'e1',
      periodMonth: 5,
      periodYear: 2024,
      amount: 100,
    });
    expect(dbMock.salaryTransaction.create).toHaveBeenCalledWith({
      data: {
        employeeId: 'e1',
        periodMonth: 5,
        periodYear: 2024,
        amount: 100,
        tenantId: 't1',
      },
    });
  });
});

import { describe, it, expect } from 'vitest';
import { calculateIncomeTax } from '@/utils/payroll-calculations';

process.env.SKIP_ENV_VALIDATION = 'true';

describe('calculateIncomeTax', () => {
  it('calculates tax for low income with standard credits', () => {
    const res = calculateIncomeTax(5000, 2.25);
    expect(res.finalTax).toBeCloseTo(449.825, 2);
  });

  it('calculates tax for higher income with extra credits', () => {
    const res = calculateIncomeTax(15000, 3.5);
    expect(res.finalTax).toBeCloseTo(1725.4, 2);
  });
});

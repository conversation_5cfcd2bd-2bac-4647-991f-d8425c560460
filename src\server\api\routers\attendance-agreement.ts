import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";

// Input validation schemas
const createAgreementSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "DRAFT"]).default("ACTIVE"),
  workDaysPerWeek: z.number().min(1).max(7),
  hoursPerDay: z.number().min(1).max(24),
  monthlyHours: z.number().min(1),
  overtimeThreshold: z.number().min(0),
  nightShiftStart: z.string().optional(),
  nightShiftEnd: z.string().optional(),
  weekendDays: z.array(z.number()).optional(),
});

const updateAgreementSchema = createAgreementSchema.extend({
  id: z.string(),
});

export const attendanceAgreementRouter = createTRPCRouter({
  // Get all agreements
  getAll: publicProcedure
    .input(
      z.object({
        status: z.enum(["ACTIVE", "INACTIVE", "DRAFT"]).optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where = input?.status ? { status: input.status } : {};
      
      return ctx.db.attendanceAgreement.findMany({
        where: {
          ...where,
          deletedAt: null,
        },
        orderBy: {
          createdAt: "desc",
        },
      });
    }),

  // Get agreement by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const agreement = await ctx.db.attendanceAgreement.findUnique({
        where: { id: input.id },
      });

      if (!agreement || agreement.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "הסכם לא נמצא",
        });
      }

      return agreement;
    }),
  // Create new agreement
  create: protectedProcedure
    .input(createAgreementSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if code already exists
      const existing = await ctx.db.attendanceAgreement.findFirst({
        where: {
          code: input.code,
          deletedAt: null,        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד הסכם כבר קיים במערכת",
        });
      }

      const { tenantId } = ctx.session.user;
      
      return ctx.db.attendanceAgreement.create({
        data: {
          ...input,
          weekendDays: input.weekendDays || [5, 6], // Default to Friday and Saturday
          tenantId,
        },
      });
    }),

  // Update agreement
  update: protectedProcedure
    .input(updateAgreementSchema)
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const { id, ...data } = input;

      // Check if agreement exists
      const existing = await ctx.db.attendanceAgreement.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "הסכם לא נמצא",
        });
      }

      // Check if new code conflicts with another agreement
      if (data.code !== existing.code) {
        const codeExists = await ctx.db.attendanceAgreement.findFirst({
          where: {
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "קוד הסכם כבר קיים במערכת",
          });
        }
      }

      return ctx.db.attendanceAgreement.update({
        where: { id },
        data,
      });
    }),

  // Delete agreement (soft delete)
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      // Check if agreement exists
      const existing = await ctx.db.attendanceAgreement.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "הסכם לא נמצא",
        });
      }

      // Check if agreement is in use
      const inUse = await ctx.db.employeeAgreement.findFirst({
        where: {
          agreementId: input.id,
          deletedAt: null,
        },
      });

      if (inUse) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק הסכם שמשויך לעובדים",
        });
      }

      return ctx.db.attendanceAgreement.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),

  // Get agreements for employee
  getByEmployeeId: publicProcedure
    .input(z.object({ employeeId: z.string() }))
    .query(async ({ ctx, input }) => {
      const employeeAgreements = await ctx.db.employeeAgreement.findMany({
        where: {
          employeeId: input.employeeId,
          deletedAt: null,
        },
        include: {
          agreement: true,
        },
        orderBy: {
          startDate: "desc",
        },
      });

      return employeeAgreements.map(ea => ({
        ...ea.agreement,
        startDate: ea.startDate,
        endDate: ea.endDate,
      }));
    }),

  // Calculate overtime hours
  calculateOvertime: publicProcedure
    .input(
      z.object({
        agreementId: z.string(),
        workedHours: z.number().min(0),
        dayType: z.enum(["WEEKDAY", "WEEKEND", "HOLIDAY"]),
      })
    )
    .query(async ({ ctx, input }) => {
      // Get agreement
      const agreement = await ctx.db.attendanceAgreement.findUnique({
        where: { id: input.agreementId },
      });

      if (!agreement || agreement.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "הסכם לא נמצא",
        });
      }

      // Get overtime rules for this agreement
      const overtimeRules = await ctx.db.overtimeRule.findMany({
        where: {
          agreementId: input.agreementId,
          dayType: input.dayType,
          deletedAt: null,
        },
        orderBy: {
          priority: "asc",
        },
      });

      // Calculate overtime
      const regularHours = Math.min(input.workedHours, agreement.overtimeThreshold);
      let remainingHours = Math.max(0, input.workedHours - agreement.overtimeThreshold);
      const overtimeHours = [];

      for (const rule of overtimeRules) {
        if (remainingHours <= 0) break;

        const ruleHours = rule.toHour 
          ? Math.min(remainingHours, (rule.toHour - rule.fromHour))
          : remainingHours;

        if (ruleHours > 0) {
          overtimeHours.push({
            rate: rule.rate,
            hours: ruleHours,
            amount: ruleHours * rule.rate, // This would be multiplied by hourly rate
          });

          remainingHours -= ruleHours;
        }
      }

      return {
        regularHours,
        overtimeHours,
        totalOvertimeHours: overtimeHours.reduce((sum, ot) => sum + ot.hours, 0),
        totalOvertimeAmount: overtimeHours.reduce((sum, ot) => sum + ot.amount, 0),
      };
    }),
}); 
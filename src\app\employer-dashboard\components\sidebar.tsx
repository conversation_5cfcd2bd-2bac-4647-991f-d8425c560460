import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  <PERSON>, 
  User, 
  <PERSON><PERSON>, 
  LogOut 
} from "lucide-react";

import {
  Sidebar as AnimatedSidebar,
  SidebarProvider,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar-animated";
import { employerDashboardNavItems } from "../navigation";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  employerName?: string;
  userName?: string;
  userEmail?: string;
  avatarFallback?: string;
  onLogout?: () => void;
}

export function Sidebar({ 
  className, 
  employerName = "חברה בע״מ", 
  userName = "משתמש", 
  userEmail = "<EMAIL>",
  avatarFallback = "MS",
  onLogout
}: SidebarProps) {
  const pathname = usePathname();

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <SidebarProvider className={cn(className, "h-full")}>
      {/* Company info with user profile */}
      <div className="flex items-center justify-between px-4 py-4 border-b">
        {/* Logo/Brand Area */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <div className="relative flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-blue-400">
            <span className="font-bold text-white text-sm">S</span>
          </div>
          <div className="flex flex-col">
            <span className="text-sm font-semibold">מערכת שכר</span>
            <span className="text-xs text-muted-foreground">{employerName}</span>
          </div>
        </div>

        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="relative h-8 w-8 rounded-full"
            >
              <Avatar className="h-8 w-8">
                <AvatarImage src="" alt={userName} />
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  {avatarFallback}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel className="text-right">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{userName}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {userEmail}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {employerName}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-right cursor-pointer">
              <User className="ml-2 h-4 w-4" />
              <span>פרופיל</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="text-right cursor-pointer">
              <Settings className="ml-2 h-4 w-4" />
              <span>הגדרות</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="text-right cursor-pointer text-red-600 focus:text-red-600"
              onClick={handleLogout}
            >
              <LogOut className="ml-2 h-4 w-4" />
              <span>התנתק</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <AnimatedSidebar side="right" className="border-none shadow-none">
        <SidebarHeader className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
            ניהול מעסיק
          </h2>
        </SidebarHeader>
        <SidebarContent>
          <SidebarMenu>
            {employerDashboardNavItems.map((item) => (
              <SidebarMenuItem key={item.href}>
                <SidebarMenuButton asChild isActive={pathname === item.href}>
                  <Link href={item.href} className="flex rtl:flex-row-reverse">
                    <span className="ml-2 rtl:ml-0 rtl:mr-2">{item.icon}</span>
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarContent>
      </AnimatedSidebar>
    </SidebarProvider>
  );
}

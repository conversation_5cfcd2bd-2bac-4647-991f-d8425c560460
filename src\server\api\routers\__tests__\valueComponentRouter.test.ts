import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { valueComponentRouter } = await import('../value-component');
const createCaller = createCallerFactory(valueComponentRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('valueComponentRouter.create', () => {
  it('rejects duplicate code', async () => {
    const dbMock = {
      valueComponent: {
        findFirst: vi.fn().mockResolvedValue({ id: 'v1' }),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock });
    await expect(
      caller.create({ code: 'C1', name: 'v', valueComponentType: 'OTHER', affectsPension: false, isOneTime: false, isActive: true })
    ).rejects.toBeInstanceOf(Error);
  });

  it('resolves tenant from db', async () => {
    const dbMock = {
      valueComponent: {
        findFirst: vi.fn().mockResolvedValue(null),
        create: vi.fn().mockResolvedValue({ id: 'v1' }),
      },
      user: {
        findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }),
      },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', tenantId: 't1' } },
    });
    await caller.create({
      code: 'C1',
      name: 'v',
      valueComponentType: 'OTHER',
      affectsPension: false,
      isOneTime: false,
      isActive: true,
    });
    expect(dbMock.user.findUnique).toHaveBeenCalledWith({
      where: { id: 'u1' },
      select: { tenantId: true },
    });
    expect(dbMock.valueComponent.create).toHaveBeenCalledWith({
      data: {
        code: 'C1',
        name: 'v',
        valueComponentType: 'OTHER',
        affectsPension: false,
        isOneTime: false,
        isActive: true,
        tenantId: 't1',
      },
    });
  });
});

describe('valueComponentRouter.update/delete authorization', () => {
  const input = {
    id: 'v1',
    code: 'C1',
    name: 'v',
    valueComponentType: 'OTHER',
    affectsPension: false,
    isOneTime: false,
    isActive: true,
  };

  it('rejects update for non-admin', async () => {
    const dbMock = { valueComponent: { findUnique: vi.fn() } } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', role: 'EMPLOYEE' } },
    });
    await expect(caller.update(input)).rejects.toBeInstanceOf(Error);
    expect(dbMock.valueComponent.findUnique).not.toHaveBeenCalled();
  });

  it('rejects delete for non-admin', async () => {
    const dbMock = { valueComponent: { findUnique: vi.fn() } } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', role: 'EMPLOYEE' } },
    });
    await expect(caller.delete({ id: 'v1' })).rejects.toBeInstanceOf(Error);
    expect(dbMock.valueComponent.findUnique).not.toHaveBeenCalled();
  });
});

import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { breakRuleRouter } = await import('../break-rule');
const createCaller = createCallerFactory(breakRuleRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('breakRuleRouter.create', () => {
  it('uses tenant from session', async () => {
    const dbMock = {
      breakRule: { create: vi.fn().mockResolvedValue({ id: 'b1' }) },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', tenantId: 't1' } },
    });
    await caller.create({
      agreementId: 'a1',
      name: 'Rule',
      minWorkHours: 4,
      breakDuration: 30,
    });
    expect(dbMock.breakRule.create).toHaveBeenCalledWith({
      data: {
        agreementId: 'a1',
        name: 'Rule',
        minWorkHours: 4,
        breakDuration: 30,
        isPaid: false,
        isMandatory: true,
        canBeSplit: false,
        tenantId: 't1',
      },
    });
  });
});

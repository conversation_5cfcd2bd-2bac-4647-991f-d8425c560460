	"use client";

	import { Button } from "@/components/ui/rtl-components";
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from "@/components/ui/rtl-components";
	import { Input } from "@/components/ui/input";
	import { useState } from "react";

	interface AttendanceRecord {
		id: number;
		date: string;
		employee: string;
		hours: number;
	}

	const SAMPLE_RECORDS: AttendanceRecord[] = [
		{ id: 1, date: "2025-05-01", employee: "ישראל ישראלי", hours: 8 },
		{ id: 2, date: "2025-05-02", employee: "דנה לוי", hours: 7.5 },
		{ id: 3, date: "2025-05-03", employee: "אורי כהן", hours: 9 },
	];

	export default function AttendancePage() {
		const [fromDate, setFromDate] = useState("");
		const [toDate, setToDate] = useState("");

		const filtered = SAMPLE_RECORDS.filter((rec) => {
			if (fromDate && rec.date < fromDate) return false;
			if (toDate && rec.date > toDate) return false;
			return true;
		});

		return (
			<div className="container mx-auto space-y-6 py-6" dir="rtl">
				<div>
					<h1 className="font-bold text-2xl">נוכחות</h1>
					<p className="text-muted-foreground">צפייה ורישום שעות נוכחות</p>
				</div>
				<div className="flex flex-wrap gap-4">
					<div>
						<label htmlFor="from" className="block font-medium text-sm">
							מתאריך
						</label>
						<Input
							id="from"
							type="date"
							value={fromDate}
							onChange={(e) => setFromDate(e.target.value)}
						/>
					</div>
					<div>
						<label htmlFor="to" className="block font-medium text-sm">
							עד תאריך
						</label>
						<Input
							id="to"
							type="date"
							value={toDate}
							onChange={(e) => setToDate(e.target.value)}
						/>
					</div>
					<Button
						variant="outline"
						onClick={() => {
							setFromDate("");
							setToDate("");
						}}
					>
						איפוס
					</Button>
				</div>
				<div className="overflow-x-auto rounded-md border">
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead className="w-24">תאריך</TableHead>
								<TableHead>עובד</TableHead>
								<TableHead>שעות</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{filtered.map((rec) => (
								<TableRow key={rec.id}>
									<TableCell>{rec.date}</TableCell>
									<TableCell>{rec.employee}</TableCell>
									<TableCell>{rec.hours}</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</div>
		);
	}

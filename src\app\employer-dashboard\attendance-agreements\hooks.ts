"use client";

import { api } from "@/trpc/react";
import type {
  AttendanceAgreement,
  Shift,
  OvertimeRule,
  BreakRule,
  Location,
  MovementType,
  EmployeeAgreement,
} from "./types";

// Movement category type to match the server-side enum
type MovementCategory = "CHECK_IN" | "CHECK_OUT" | "BREAK_START" | "BREAK_END" | "OTHER";

// ============================================
// Attendance Agreements Hooks
// ============================================

export function useAgreements(status?: "ACTIVE" | "INACTIVE" | "DRAFT") {
  return api.attendanceAgreement.getAll.useQuery({ status });
}

export function useAgreement(id: string) {
  return api.attendanceAgreement.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateAgreement() {
    const utils = api.useUtils();
  
  return api.attendanceAgreement.create.useMutation({
    onSuccess: () => {
      utils.attendanceAgreement.getAll.invalidate();
    },
  });
}

export function useUpdateAgreement() {
    const utils = api.useUtils();
  
  return api.attendanceAgreement.update.useMutation({
    onSuccess: (data) => {
      utils.attendanceAgreement.getAll.invalidate();
      utils.attendanceAgreement.getById.invalidate({ id: data.id });
    },
  });
}

export function useDeleteAgreement() {
    const utils = api.useUtils();
  
  return api.attendanceAgreement.delete.useMutation({
    onSuccess: () => {
      utils.attendanceAgreement.getAll.invalidate();
    },
  });
}

// ============================================
// Shifts Hooks
// ============================================

export function useShifts(agreementId?: string) {
  return api.shift.getAll.useQuery({ agreementId });
}

export function useShift(id: string) {
  return api.shift.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateShift() {
    const utils = api.useUtils();
  
  return api.shift.create.useMutation({
    onSuccess: () => {
      utils.shift.getAll.invalidate();
    },
  });
}

export function useUpdateShift() {
    const utils = api.useUtils();
  
  return api.shift.update.useMutation({
    onSuccess: (data) => {
      utils.shift.getAll.invalidate();
      utils.shift.getById.invalidate({ id: data.id });
    },
  });
}

export function useDeleteShift() {
    const utils = api.useUtils();
  
  return api.shift.delete.useMutation({
    onSuccess: () => {
      utils.shift.getAll.invalidate();
    },
  });
}

// ============================================
// Overtime Rules Hooks
// ============================================

export function useOvertimeRules(agreementId?: string) {
  return api.overtimeRule.getAll.useQuery({ agreementId });
}

export function useOvertimeRule(id: string) {
  return api.overtimeRule.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateOvertimeRule() {
    const utils = api.useUtils();
  
  return api.overtimeRule.create.useMutation({
    onSuccess: () => {
      utils.overtimeRule.getAll.invalidate();
    },
  });
}

export function useUpdateOvertimeRule() {
    const utils = api.useUtils();
  
  return api.overtimeRule.update.useMutation({
    onSuccess: (data) => {
      utils.overtimeRule.getAll.invalidate();
      utils.overtimeRule.getById.invalidate({ id: data.id });
    },
  });
}

export function useDeleteOvertimeRule() {
    const utils = api.useUtils();
  
  return api.overtimeRule.delete.useMutation({
    onSuccess: () => {
      utils.overtimeRule.getAll.invalidate();
    },
  });
}

// ============================================
// Break Rules Hooks
// ============================================

export function useBreakRules(agreementId?: string) {
  return api.breakRule.getAll.useQuery({ agreementId });
}

export function useBreakRule(id: string) {
  return api.breakRule.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateBreakRule() {
    const utils = api.useUtils();
  
  return api.breakRule.create.useMutation({
    onSuccess: () => {
      utils.breakRule.getAll.invalidate();
    },
  });
}

export function useUpdateBreakRule() {
    const utils = api.useUtils();
  
  return api.breakRule.update.useMutation({
    onSuccess: (data) => {
      utils.breakRule.getAll.invalidate();
      utils.breakRule.getById.invalidate({ id: data.id });
    },
  });
}

export function useDeleteBreakRule() {
    const utils = api.useUtils();
  
  return api.breakRule.delete.useMutation({
    onSuccess: () => {
      utils.breakRule.getAll.invalidate();
    },
  });
}

// ============================================
// Locations Hooks
// ============================================

export function useLocations(isActive?: boolean) {
  return api.location.getAll.useQuery({ isActive });
}

export function useLocation(id: string) {
  return api.location.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateLocation() {
    const utils = api.useUtils();
  
  return api.location.create.useMutation({
    onSuccess: () => {
      utils.location.getAll.invalidate();
    },
  });
}

export function useUpdateLocation() {
    const utils = api.useUtils();
  
  return api.location.update.useMutation({
    onSuccess: (data) => {
      utils.location.getAll.invalidate();
      utils.location.getById.invalidate({ id: data.id });
    },
  });
}

export function useDeleteLocation() {
    const utils = api.useUtils();
  
  return api.location.delete.useMutation({
    onSuccess: () => {
      utils.location.getAll.invalidate();
    },
  });
}

// ============================================
// Movement Types Hooks
// ============================================

export function useMovementTypes(category?: MovementCategory) {
  return api.movementType.getAll.useQuery({ category });
}

export function useMovementType(id: string) {
  return api.movementType.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateMovementType() {
    const utils = api.useUtils();
  
  return api.movementType.create.useMutation({
    onSuccess: () => {
      utils.movementType.getAll.invalidate();
    },
  });
}

export function useUpdateMovementType() {
    const utils = api.useUtils();
  
  return api.movementType.update.useMutation({
    onSuccess: (data) => {
      utils.movementType.getAll.invalidate();
      utils.movementType.getById.invalidate({ id: data.id });
    },
  });
}

export function useDeleteMovementType() {
    const utils = api.useUtils();
  
  return api.movementType.delete.useMutation({
    onSuccess: () => {
      utils.movementType.getAll.invalidate();
    },
  });
}

// ============================================
// Employee Agreements Hooks
// ============================================

export function useEmployeeAgreements(employeeId?: string) {
  return api.employeeAgreement.getAll.useQuery({ employeeId });
}

export function useCreateEmployeeAgreement() {
    const utils = api.useUtils();
  
  return api.employeeAgreement.create.useMutation({
    onSuccess: () => {
      utils.employeeAgreement.getAll.invalidate();
    },
  });
}

export function useUpdateEmployeeAgreement() {
    const utils = api.useUtils();
  
  return api.employeeAgreement.update.useMutation({
    onSuccess: () => {
      utils.employeeAgreement.getAll.invalidate();
    },
  });
}

export function useDeleteEmployeeAgreement() {
    const utils = api.useUtils();
  
  return api.employeeAgreement.delete.useMutation({
    onSuccess: () => {
      utils.employeeAgreement.getAll.invalidate();
    },
  });
}

// ============================================
// Utility Hooks
// ============================================

export function useCalculateOvertimeHours(
  agreementId: string,
  workedHours: number,
  dayType: "WEEKDAY" | "WEEKEND" | "HOLIDAY"
) {
  return api.attendanceAgreement.calculateOvertime.useQuery(
    { agreementId, workedHours, dayType },
    { enabled: !!agreementId && workedHours > 0 }
  );
}

export function useValidateShiftOverlap(
  agreementId: string,
  startTime: string,
  endTime: string,
  excludeShiftId?: string
) {
  return api.shift.validateOverlap.useQuery(
    { agreementId, startTime, endTime, excludeShiftId },
    { enabled: !!agreementId && !!startTime && !!endTime }
  );
} 
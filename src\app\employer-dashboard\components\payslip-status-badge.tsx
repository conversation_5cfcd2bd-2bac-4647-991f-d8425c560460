import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { PayslipStatus } from "@prisma/client";

interface PayslipStatusBadgeProps {
  status: PayslipStatus;
}

export function PayslipStatusBadge({ status }: PayslipStatusBadgeProps) {
  const getStatusInfo = (status: PayslipStatus): { label: string; variant: "default" | "secondary" | "destructive" | "outline"; className: string } => {
    switch (status) {
      case "DRAFT":
        return {
          label: "טיוטה",
          variant: "outline",
          className: "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300",
        };
      case "CALCULATED":
        return {
          label: "ממתין לאישור",
          variant: "secondary",
          className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-300",
        };
      case "APPROVED":
        return {
          label: "מאושר",
          variant: "default",
          className: "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-300",
        };
      case "SENT":
        return {
          label: "נשלח",
          variant: "secondary",
          className: "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300",
        };
      case "PAID":
        return {
          label: "שולם",
          variant: "default",
          className: "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300",
        };
      case "CANCELLED":
        return {
          label: "בוטל",
          variant: "destructive",
          className: "bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-300",
        };
      default:
        return {
          label: String(status),
          variant: "outline",
          className: "",
        };
    }
  };

  const { label, variant, className } = getStatusInfo(status);

  return (
    <Badge
      variant={variant}
      className={cn("text-xs font-normal", className)}
    >
      {label}
    </Badge>
  );
} 
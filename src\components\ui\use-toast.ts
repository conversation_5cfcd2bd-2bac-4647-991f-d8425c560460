// Inspired by react-hot-toast library
import { useState, useEffect, useCallback, type ReactNode } from "react";
import { toast as sonnerToast } from "sonner";

type ToastProps = {
  title?: string;
  description?: string;
  action?: ReactNode;
  variant?: "default" | "destructive" | "success";
  duration?: number;
};

// Export the useToast hook
export const useToast = () => {
  const toast = useCallback(
    ({ title, description, action, variant = "default", duration = 5000 }: ToastProps) => {
      const variantStyles = {
        default: {},
        destructive: {
          style: {
            backgroundColor: "var(--destructive)",
            color: "var(--destructive-foreground)",
            border: "1px solid var(--destructive)",
          },
        },
        success: {
          style: {
            backgroundColor: "var(--success)",
            color: "var(--success-foreground)",
            border: "1px solid var(--success)",
          },
        },
      };

      sonnerToast(title || "", {
        description,
        action,
        duration,
        ...(variant !== "default" ? variantStyles[variant] : {}),
      });
    },
    []
  );

  return { toast };
};

// Export a Toast component for convenience
export type { ToastProps }; 
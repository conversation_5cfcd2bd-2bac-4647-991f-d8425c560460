import {
	calculateIncomeTax,
	calculateNationalInsurance,
	calculateOvertimePay,
} from "@/utils/payroll-calculations";
import { Prisma } from "@prisma/client";
import { describe, expect, it, vi } from "vitest";
import { calculatePayslip } from "../payslip";

describe("calculateIncomeTax", () => {
	it("computes income tax with credits", () => {
		const res = calculateIncomeTax(10000, 2.25);
		expect(res.taxCreditsValue).toBe(2.25 * 223);
		expect(res.finalTax).toBeGreaterThan(0);
	});
});

describe("calculateNationalInsurance", () => {
	it("computes NI for resident worker", () => {
		const res = calculateNationalInsurance(8000, true, 30);
		expect(res.employeeNI).toBeCloseTo(8000 * 0.07);
		expect(res.employerNI).toBeCloseTo(8000 * 0.076);
		expect(res.totalNI).toBeCloseTo(
			res.employeeNI + res.employerNI + res.healthInsurance,
		);
	});
});

describe("calculateOvertimePay", () => {
	it("calculates valid overtime payment", () => {
		const res = calculateOvertimePay(160, 10, 5, 0, 0, 50);
		expect(res.isValid).toBe(true);
		expect(res.totalPay).toBeCloseTo(160 * 50 + 10 * 50 * 1.25 + 5 * 50 * 1.5);
	});
});

describe("calculatePayslip", () => {
	it("creates payslip for monthly employee", async () => {
		const employee = {
			id: "emp1",
			tenantId: "t1",
			employerId: "e1",
			isForeign: false,
			birthDate: new Date(1990, 0, 1),
			salaryRecords: [{ basis: "MONTHLY", amount: new Prisma.Decimal(10000) }],
			employeeAgreements: [],
			associations: [],
			department: null,
		} as Record<string, unknown>;

		const payslipResult = { id: "p1", items: [] } as Record<string, unknown>;

		const db: Record<string, unknown> = {
			employee: { findUnique: vi.fn().mockResolvedValue(employee) },
			salaryTransaction: {
				findMany: vi.fn().mockResolvedValue([]),
				updateMany: vi.fn(),
			},
			salaryTemplateComponent: { findMany: vi.fn().mockResolvedValue([]) },
			paymentComponent: { findMany: vi.fn().mockResolvedValue([]) },
			formula: { findMany: vi.fn().mockResolvedValue([]) },
			deductionComponent: { findMany: vi.fn().mockResolvedValue([]) },
			valueComponent: { findMany: vi.fn().mockResolvedValue([]) },
			form101: { findFirst: vi.fn().mockResolvedValue(null) },
			payslip: {
				findFirst: vi.fn().mockResolvedValue(null),
				create: vi.fn().mockResolvedValue(payslipResult),
			},
		};

		db.$transaction = vi.fn(async (fn) => fn(db));

		const res = await calculatePayslip(db as unknown as PrismaClient, {
			employeeId: "emp1",
			month: 1,
			year: 2025,
		});

		expect(db.payslip.create).toHaveBeenCalled();
		expect(res).toBe(payslipResult);
	});

	it("throws when employee not found", async () => {
		const db: Record<string, unknown> = {
			employee: { findUnique: vi.fn().mockResolvedValue(null) },
		};
		db.$transaction = vi.fn(async (fn) => fn(db));
		await expect(
			calculatePayslip(db as unknown as PrismaClient, {
				employeeId: "emp1",
				month: 1,
				year: 2025,
			}),
		).rejects.toThrow();
	});

});

import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { userRouter } = await import('../user');
const createCaller = createCallerFactory(userRouter);

const baseCtx = {
  db: {},
  logger: undefined,
  headers: new Headers(),
} as any;

describe('userRouter.getAll', () => {
  it('rejects mismatched employerId for regular user', async () => {
    const dbMock = { user: { findUnique: vi.fn() } } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', role: 'EMPLOYEE', employerId: 'emp1', tenantId: 't1' } },
    });
    await expect(
      caller.getAll({ page: 1, limit: 10, employerId: 'emp2' })
    ).rejects.toBeInstanceOf(Error);
    expect(dbMock.user.findUnique).not.toHaveBeenCalled();
  });

  it('resolves tenant from db', async () => {
    const dbMock = {
      user: {
        findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }),
        count: vi.fn().mockResolvedValue(0),
        findMany: vi.fn().mockResolvedValue([]),
      },
      employer: { findMany: vi.fn().mockResolvedValue([]) },
      auditLog: { findMany: vi.fn().mockResolvedValue([]) },
    } as any;

    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', role: 'ADMIN', tenantId: 't1' } },
    });

    const res = await caller.getAll({ page: 1, limit: 10 });
    expect(dbMock.user.findUnique).toHaveBeenCalledWith({
      where: { id: 'u1' },
      select: { tenantId: true },
    });
    expect(res).toEqual({ users: [], totalCount: 0, pageCount: 0 });
  });

  it('throws NOT_FOUND for missing user', async () => {
    const dbMock = {
      user: {
        findUnique: vi.fn().mockResolvedValue(null),
      },
    } as any;

    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', role: 'ADMIN', tenantId: 't1' } },
    });

    await expect(caller.getAll({ page: 1, limit: 10 })).rejects.toBeInstanceOf(Error);
    expect(dbMock.user.findUnique).toHaveBeenCalled();
  });
});

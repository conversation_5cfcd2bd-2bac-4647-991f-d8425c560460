generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id                   String                      @id @default(uuid()) @db.Uuid
  name                 String                      @unique
  plan                 String?
  createdAt            DateTime                    @default(now())
  updatedAt            DateTime                    @updatedAt
  alerts               Alert[]
  auditLogs            AuditLog[]
  bankAccounts         BankAccount[]
  departments          Department[]
  documentSettings     DocumentSettings?
  documents            Document[]
  employees            Employee[]
  employers            Employer[]
  form101s             Form101[]
  form102s             Form102[]
  form106s             Form106[]
  form126s             Form126[]
  importExports        ImportExportHistory[]
  leaveRecords         LeaveRecord[]
  insurance            NationalInsuranceRecord[]
  payslips             Payslip[]
  providentFunds       ProvidentFund[]
  contributions        ProvidentFundContribution[]
  reports              Report[]
  salaryRecords        SalaryRecord[]
  salaryTransactions   SalaryTransaction[]
  signatureRequests    SignatureRequest[]
  smsLogs              SmsLog[]
  users                User[]
  employeeRoles        EmployeeRole[]
  salaryTemplates      SalaryTemplate[]
  salaryAgreements     SalaryAgreement[]
  associations         Association[]
  attendanceAgreements AttendanceAgreement[]
  employeeAgreements   EmployeeAgreement[]
  shifts               Shift[]
  overtimeRules        OvertimeRule[]
  breakRules           BreakRule[]
  locations            Location[]
  movementTypes        MovementType[]
  movements            Movement[]
  deductionComponents  DeductionComponent[]
  formulas             Formula[]
  paymentComponents    PaymentComponent[]
  valueComponents      ValueComponent[]
  taxBrackets          TaxBracket[]
  nationalInsuranceRules NationalInsuranceRule[]
  salaryComponents     SalaryComponent[]
  salaryRules          SalaryRule[]
  settings             Setting[]
}

model User {
  id                 String                @id @default(uuid()) @db.Uuid
  tenantId           String                @db.Uuid
  email              String
  name               String?
  role               Role
  password           String
  isActive           Boolean               @default(true)
  mustResetPassword  Boolean               @default(false)
  employerId         String?               @db.Uuid
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  preferences        UserPreferences?
  alerts             Alert[]
  auditLogs          AuditLog[]
  importExports      ImportExportHistory[]
  reports            Report[]
  salaryTransactions SalaryTransaction[]
  uploadedDocuments  Document[]            @relation(name: "DocumentUploadedBy")
  employer           Employer?             @relation(name: "UserToEmployer", fields: [employerId], references: [id], onDelete: SetNull)
  tenant             Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId, email])
  @@index([tenantId])
  @@index([employerId])
  @@index([role])
  @@index([isActive])
  @@index([createdAt])
}

model Alert {
  id         String         @id @default(uuid()) @db.Uuid
  tenantId   String         @db.Uuid
  userId     String?        @db.Uuid
  employeeId String?        @db.Uuid
  type       AlertType      @default(INFO)
  category   AlertCategory?
  message    String
  isRead     Boolean        @default(false)
  readAt     DateTime?
  dueDate    DateTime?
  severity   String?
  isResolved Boolean        @default(false)
  resolvedAt DateTime?
  createdAt  DateTime       @default(now())
  context    AlertContext?
  employee   Employee?      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant     Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user       User?          @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([userId])
  @@index([employeeId])
  @@index([category])
  @@index([isResolved])
}

model AuditLog {
  id        String           @id @default(uuid()) @db.Uuid
  tenantId  String           @db.Uuid
  modelName String
  recordId  String?          @db.Uuid
  action    AuditAction
  userId    String?          @db.Uuid
  userEmail String?
  ipAddress String?
  timestamp DateTime         @default(now())
  values    AuditLogValues[]
  tenant    Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user      User?            @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([modelName, recordId])
  @@index([userId])
}

model Employer {
  id                  String               @id @default(uuid()) @db.Uuid
  tenantId            String               @db.Uuid
  name                String
  identifier          String?
  taxId               String?
  niNumber            String?
  companyId           String?
  industry            Sector?
  registrationDate    DateTime?
  accountManager      String?
  payrollDay          Int?
  profilePictureUrl   String?
  profilePictureKey   String?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  address             EmployerAddress?
  contact             EmployerContact?
  bankAccounts        BankAccount[]
  departments         Department[]
  documents           Document[]           @relation(name: "DocumentToEmployer")
  employees           Employee[]
  tenant              Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  forms102            Form102[]
  forms126            Form126[]
  reports             Report[]
  users               User[]               @relation(name: "UserToEmployer")
  roles               Role[]
  salaryTemplates     SalaryTemplate[]
  salaryAgreements    SalaryAgreement[]
  employeeRoles       EmployeeRole[]
  formulas            Formula[]
  deductionComponents DeductionComponent[]
  paymentComponents   PaymentComponent[]
  valueComponents     ValueComponent[]

  @@unique([tenantId, name])
  @@unique([tenantId, companyId])
  @@index([tenantId])
  @@index([industry])
  @@index([profilePictureUrl])
  @@index([registrationDate])
  @@index([createdAt])
}

model Department {
  id                 String              @id @default(uuid()) @db.Uuid
  tenantId           String              @db.Uuid
  employerId         String              @db.Uuid
  name               String
  code               String?
  description        String?
  isActive           Boolean             @default(true)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  employer           Employer            @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant             Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employees          Employee[]
  payslipItems       PayslipItem[]
  salaryTransactions SalaryTransaction[]
  associations       Association[]

  @@unique([employerId, name])
  @@unique([employerId, code])
  @@index([tenantId])
  @@index([employerId])
}

model BankAccount {
  id            String       @id @default(uuid()) @db.Uuid
  tenantId      String       @db.Uuid
  bankName      String
  branchCode    String?
  accountNumber String
  bankCode      Int?
  branchNumber  Int?
  currency      Currency     @default(ILS)
  accountType   AccountType?
  isPrimary     Boolean      @default(false)
  percentage    Decimal?     @db.Decimal(5, 2)
  employeeId    String?      @db.Uuid
  employerId    String?      @db.Uuid
  effectiveFrom DateTime?
  effectiveTo   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  employee      Employee?    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  employer      Employer?    @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant        Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([employerId])
}

model Employee {
  id                  String                      @id @default(uuid()) @db.Uuid
  tenantId            String                      @db.Uuid
  employerId          String                      @db.Uuid
  departmentId        String?                     @db.Uuid
  firstName           String
  lastName            String
  nationalId          String
  status              EmployeeStatus              @default(ACTIVE)
  birthDate           DateTime?
  address             Json?
  contact             Json?
  startDate           DateTime
  endDate             DateTime?
  profilePictureUrl   String?
  profilePictureKey   String?
  isForeign           Boolean                     @default(false)
  country             String?
  sector              Sector?
  agreementType       AgreementType?
  isResidentForNI     Boolean                     @default(true)
  visaNumber          String?
  visaExpiry          DateTime?
  visaType            String?
  baseSalary          Decimal?                    @db.Decimal(12, 2)
  travelAllowance     Decimal?                    @db.Decimal(12, 2)
  primaryAssignment   String?
  secondaryAssignment String?
  isControllingOwner  Boolean?
  isUnsupervised      Boolean?
  terminationReason   String?
  assignments         Json?
  createdAt           DateTime                    @default(now())
  updatedAt           DateTime                    @updatedAt
  alerts              Alert[]
  bankAccounts        BankAccount[]
  documents           Document[]                  @relation(name: "DocumentToEmployee")
  department          Department?                 @relation(fields: [departmentId], references: [id])
  employer            Employer                    @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant              Tenant                      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  form101s            Form101[]
  form106s            Form106[]
  leaveRecords        LeaveRecord[]
  insurance           NationalInsuranceRecord[]
  payslips            Payslip[]
  provident           ProvidentFundContribution[]
  salaryRecords       SalaryRecord[]
  salaryTransactions  SalaryTransaction[]
  smsLogs             SmsLog[]
  associations        Association[]
  employeeAgreements  EmployeeAgreement[]
  movements           Movement[]
  EmployeeAddress     EmployeeAddress?
  EmployeeContact     EmployeeContact?
  EmployeeAssignment  EmployeeAssignment[]

  @@unique([tenantId, nationalId])
  @@index([tenantId])
  @@index([employerId])
  @@index([nationalId])
  @@index([tenantId, employerId])
  @@index([profilePictureUrl])
  @@index([isForeign])
  @@index([sector])
  @@index([visaExpiry])
  @@index([departmentId])
}

model SalaryRecord {
  id                   String        @id @default(uuid()) @db.Uuid
  tenantId             String        @db.Uuid
  employeeId           String        @db.Uuid
  basis                Basis         @default(MONTHLY)
  currency             Currency      @default(ILS)
  payFrequency         PayFrequency  @default(MONTHLY)
  amount               Decimal       @db.Decimal(12, 2)
  effectiveFrom        DateTime
  effectiveTo          DateTime?
  positionType         PositionType?
  hourlyRate           Decimal?      @db.Decimal(12, 2)
  dailyRate            Decimal?      @db.Decimal(12, 2)
  workedHours          Decimal?      @db.Decimal(8, 2)
  workedDays           Decimal?      @db.Decimal(5, 2)
  standardMonthlyHours Decimal?      @db.Decimal(8, 2)
  standardDailyHours   Decimal?      @db.Decimal(5, 2)
  standardMonthDays    Int?
  workDaysPerWeek      Int?
  positionPercentage   Int?
  createdAt            DateTime      @default(now())
  updatedAt            DateTime      @updatedAt
  employee             Employee      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant               Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
}

model Payslip {
  id                String                      @id @default(uuid()) @db.Uuid
  tenantId          String                      @db.Uuid
  employeeId        String                      @db.Uuid
  year              Int
  month             Int
  periodStart       DateTime?
  periodEnd         DateTime?
  status            PayslipStatus               @default(DRAFT)
  grossPay          Decimal                     @db.Decimal(12, 2)
  netPay            Decimal                     @db.Decimal(12, 2)
  taxDeducted       Decimal                     @db.Decimal(12, 2)
  insuranceDeducted Decimal                     @db.Decimal(12, 2)
  otherDeductions   Decimal?                    @db.Decimal(12, 2)
  allowances        Decimal?                    @db.Decimal(12, 2)
  currency          Currency                    @default(ILS)
  healthInsurance   Decimal?                    @db.Decimal(12, 2)
  pensionEmployee   Decimal?                    @db.Decimal(12, 2)
  pensionEmployer   Decimal?                    @db.Decimal(12, 2)
  severancePay      Decimal?                    @db.Decimal(12, 2)
  netDeposit        Decimal?                    @db.Decimal(12, 2)
  breakdown         Json?
  issuedAt          DateTime                    @default(now())
  approvedAt        DateTime?
  sentAt            DateTime?
  createdAt         DateTime                    @default(now())
  updatedAt         DateTime                    @updatedAt
  documentRelations DocumentToPayslip[]
  employee          Employee                    @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant            Tenant                      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  items             PayslipItem[]
  contributions     ProvidentFundContribution[]
  PayslipBreakdown  PayslipBreakdown[]

  @@unique([employeeId, year, month])
  @@index([tenantId])
  @@index([employeeId])
  @@index([year, month])
  @@index([status])
}

model PayslipItem {
  id                   String              @id @default(uuid()) @db.Uuid
  payslipId            String              @db.Uuid
  description          String
  amount               Decimal             @db.Decimal(12, 2)
  type                 PayslipItemType
  kod                  PayslipItemKod?
  rate                 Decimal?            @db.Decimal(12, 2)
  units                Decimal?            @db.Decimal(12, 2)
  percentage           Decimal?            @db.Decimal(5, 2)
  isDebit              Boolean             @default(false)
  isInfo               Boolean             @default(false)
  departmentId         String?             @db.Uuid
  deductionComponentId String?             @db.Uuid
  paymentComponentId   String?             @db.Uuid
  note                 String?
  deletedAt            DateTime?
  department           Department?         @relation(fields: [departmentId], references: [id])
  payslip              Payslip             @relation(fields: [payslipId], references: [id], onDelete: Cascade)
  deductionComponent   DeductionComponent? @relation(fields: [deductionComponentId], references: [id])
  paymentComponent     PaymentComponent?  @relation(fields: [paymentComponentId], references: [id], onDelete: SetNull)

  @@index([payslipId])
  @@index([kod])
  @@index([type])
  @@index([departmentId])
  @@index([deductionComponentId])
  @@index([paymentComponentId])
  @@index([deletedAt])
}

model SalaryTransaction {
  id            String          @id @default(uuid()) @db.Uuid
  tenantId      String          @db.Uuid
  employeeId    String          @db.Uuid
  periodMonth   Int
  periodYear    Int
  componentCode PayslipItemKod?
  description   String?
  quantity      Decimal?        @db.Decimal(12, 2)
  rate          Decimal?        @db.Decimal(12, 2)
  percentage    Decimal?        @db.Decimal(5, 2)
  amount        Decimal?        @db.Decimal(12, 2)
  fromDate      DateTime?
  toDate        DateTime?
  departmentId  String?         @db.Uuid
  source        String?
  isGrossedUp   Boolean         @default(false)
  isProcessed   Boolean         @default(false)
  userId        String?         @db.Uuid
  note          String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  department    Department?     @relation(fields: [departmentId], references: [id])
  employee      Employee        @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant        Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user          User?           @relation(fields: [userId], references: [id])

  @@index([tenantId])
  @@index([employeeId])
  @@index([periodYear, periodMonth])
  @@index([isProcessed])
  @@index([departmentId])
  @@index([userId])
}

model ProvidentFund {
  id            String                      @id @default(uuid()) @db.Uuid
  tenantId      String                      @db.Uuid
  name          String
  fundNumber    String
  fundType      FundType                    @default(PENSION)
  tenant        Tenant                      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  contributions ProvidentFundContribution[]

  @@unique([tenantId, fundNumber])
  @@index([tenantId])
}

model ProvidentFundContribution {
  id             String        @id @default(uuid()) @db.Uuid
  tenantId       String        @db.Uuid
  employeeId     String        @db.Uuid
  fundId         String        @db.Uuid
  payslipId      String?       @db.Uuid
  year           Int
  month          Int
  employeeAmount Decimal       @db.Decimal(12, 2)
  employerAmount Decimal       @db.Decimal(12, 2)
  severancePay   Decimal       @db.Decimal(12, 2)
  totalAmount    Decimal       @db.Decimal(12, 2)
  capApplied     Boolean       @default(false)
  fundName       String?
  employee       Employee      @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  fund           ProvidentFund @relation(fields: [fundId], references: [id], onDelete: Cascade)
  payslip        Payslip?      @relation(fields: [payslipId], references: [id], onDelete: Cascade)
  tenant         Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, fundId, year, month])
  @@index([tenantId])
  @@index([employeeId])
  @@index([fundId])
  @@index([year, month])
  @@index([tenantId, year, month])
  @@index([payslipId])
}

model NationalInsuranceRecord {
  id         String   @id @default(uuid()) @db.Uuid
  tenantId   String   @db.Uuid
  employeeId String   @db.Uuid
  year       Int
  month      Int
  employeeNI Decimal  @db.Decimal(12, 2)
  employerNI Decimal  @db.Decimal(12, 2)
  healthNI   Decimal  @db.Decimal(12, 2)
  totalNI    Decimal  @db.Decimal(12, 2)
  earnings   Decimal  @db.Decimal(12, 2)
  employee   Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant     Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, year, month])
  @@index([tenantId])
  @@index([employeeId])
  @@index([year, month])
}

model LeaveRecord {
  id              String    @id @default(uuid()) @db.Uuid
  tenantId        String    @db.Uuid
  employeeId      String    @db.Uuid
  leaveType       LeaveType
  leaveTypeCode   Int?
  startDate       DateTime
  endDate         DateTime
  entitlement     Decimal?  @db.Decimal(8, 2)
  taken           Decimal?  @db.Decimal(8, 2)
  previousBalance Decimal?  @db.Decimal(8, 2)
  notes           String?
  autoCalculated  Boolean?
  year            Int
  month           Int
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  employee        Employee  @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant          Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([leaveType])
  @@index([startDate, endDate])
  @@index([year, month])
}

model Form101 {
  id                        String             @id @default(uuid()) @db.Uuid
  tenantId                  String             @db.Uuid
  employeeId                String             @db.Uuid
  taxYear                   Int?
  maritalStatus             MaritalStatus?
  spouseWorks               Boolean            @default(false)
  childrenCount             Int                @default(0)
  childrenUnder5            Int                @default(0)
  childrenUnder18           Int                @default(0)
  additionalCreditPoints    Decimal?           @db.Decimal(4, 2)
  isMainEmployer            Boolean            @default(true)
  hasAdditionalIncome       Boolean            @default(false)
  taxCoordinationNumber     String?
  exemptionPercentage       Decimal?           @db.Decimal(5, 2)
  overrideTaxRate           Decimal?           @db.Decimal(5, 2)
  status                    Form101Status      @default(DRAFT)
  signatureRequestId        String?
  signatureRequestSentAt    DateTime?
  signatureRequestExpiresAt DateTime?
  signedAt                  DateTime?
  documentUrl               String?
  createdAt                 DateTime           @default(now())
  updatedAt                 DateTime           @updatedAt
  employee                  Employee           @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  signatureRequests         SignatureRequest[]
  tenant                    Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, taxYear])
  @@index([tenantId])
  @@index([employeeId])
  @@index([taxYear])
}

model Form102 {
  id          String    @id @default(uuid()) @db.Uuid
  tenantId    String    @db.Uuid
  employerId  String    @db.Uuid
  year        Int
  month       Int
  data        Json
  submittedAt DateTime?
  documentId  String?   @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  document    Document? @relation(fields: [documentId], references: [id])
  employer    Employer  @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employerId, year, month])
  @@index([tenantId])
  @@index([employerId])
  @@index([year, month])
}

model Form106 {
  id          String    @id @default(uuid()) @db.Uuid
  tenantId    String    @db.Uuid
  employeeId  String    @db.Uuid
  year        Int
  data        Json
  generatedAt DateTime?
  documentId  String?   @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  document    Document? @relation(fields: [documentId], references: [id])
  employee    Employee  @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employeeId, year])
  @@index([tenantId])
  @@index([employeeId])
  @@index([year])
}

model Form126 {
  id          String    @id @default(uuid()) @db.Uuid
  tenantId    String    @db.Uuid
  employerId  String    @db.Uuid
  year        Int
  data        Json
  submittedAt DateTime?
  documentId  String?   @db.Uuid
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  document    Document? @relation(fields: [documentId], references: [id])
  employer    Employer  @relation(fields: [employerId], references: [id], onDelete: Cascade)
  tenant      Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([employerId, year])
  @@index([tenantId])
  @@index([employerId])
  @@index([year])
}

model Report {
  id               String            @id @default(uuid()) @db.Uuid
  tenantId         String            @db.Uuid
  employerId       String?           @db.Uuid
  type             ReportType
  year             Int?
  month            Int?
  parameters       Json?
  filePath         String?
  generatedBy      String?           @db.Uuid
  generatedAt      DateTime          @default(now())
  version          Int               @default(1)
  employer         Employer?         @relation(fields: [employerId], references: [id], onDelete: Cascade)
  generator        User?             @relation(fields: [generatedBy], references: [id])
  tenant           Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  ReportParameters ReportParameters?

  @@index([tenantId])
  @@index([employerId])
  @@index([year, month])
  @@index([type])
}

model Document {
  id               String              @id @default(uuid()) @db.Uuid
  updatedAt        DateTime            @updatedAt
  tenantId         String              @db.Uuid
  employerId       String?             @db.Uuid
  employeeId       String?             @db.Uuid
  category         String?
  fileName         String
  title            String?
  description      String?
  fileSize         Int
  mimeType         String
  uploadedBy       String              @db.Uuid
  uploadedAt       DateTime            @default(now())
  url              String?
  s3Key            String?
  referenceModel   String?
  referenceId      String?             @db.Uuid
  metadata         Json?
  fileType         String?
  employee         Employee?           @relation(name: "DocumentToEmployee", fields: [employeeId], references: [id], onDelete: SetNull)
  employer         Employer?           @relation(name: "DocumentToEmployer", fields: [employerId], references: [id], onDelete: SetNull)
  tenant           Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  uploadedByUser   User                @relation(name: "DocumentUploadedBy", fields: [uploadedBy], references: [id], onDelete: Restrict)
  payslipRelations DocumentToPayslip[]
  form102s         Form102[]
  form106s         Form106[]
  form126s         Form126[]
  DocumentMetadata DocumentMetadata?

  @@index([tenantId])
  @@index([employerId])
  @@index([employeeId])
  @@index([category])
  @@index([uploadedBy])
  @@index([uploadedAt])
  @@index([mimeType])
  @@index([fileSize])
  @@index([referenceModel, referenceId])
}

model DocumentToPayslip {
  documentId String   @db.Uuid
  payslipId  String   @db.Uuid
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  payslip    Payslip  @relation(fields: [payslipId], references: [id], onDelete: Cascade)

  @@id([documentId, payslipId])
}

model ImportExportHistory {
  id                  String               @id @default(uuid()) @db.Uuid
  tenantId            String               @db.Uuid
  userId              String?              @db.Uuid
  action              TransferAction
  entity              String
  fileName            String?
  recordCount         Int?
  status              String
  details             Json?
  startedAt           DateTime             @default(now())
  finishedAt          DateTime?
  tenant              Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user                User?                @relation(fields: [userId], references: [id])
  ImportExportDetails ImportExportDetails?

  @@index([tenantId])
  @@index([userId])
  @@index([action, entity])
  @@index([status])
  @@index([startedAt])
}

model DocumentSettings {
  tenantId                String                    @id @db.Uuid
  allowedCategories       Json
  tenant                  Tenant                    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  AllowedDocumentCategory AllowedDocumentCategory[]

  @@index([tenantId])
}

// ============================================
// NEW RELATIONAL MODELS (replacing JSON fields)
// ============================================

// Replace User.preferences (Json?)
model UserPreferences {
  id                   String   @id @default(uuid()) @db.Uuid
  userId               String   @unique @db.Uuid
  language             String?  @default("he")
  timezone             String?  @default("Asia/Jerusalem")
  dateFormat           String?  @default("DD/MM/YYYY")
  theme                String?  @default("light")
  notificationsEnabled Boolean  @default(true)
  emailNotifications   Boolean  @default(true)
  smsNotifications     Boolean  @default(false)
  dashboardLayout      String?
  defaultCurrency      String?  @default("ILS")
  defaultPayrollView   String?
  autoSaveEnabled      Boolean  @default(true)
  compactMode          Boolean  @default(false)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

// Replace Alert.context (Json?)
model AlertContext {
  id               String   @id @default(uuid()) @db.Uuid
  alertId          String   @unique @db.Uuid
  entityType       String?
  entityId         String?  @db.Uuid
  actionType       String?
  messageParams    String?  @db.Text
  metadata         String?  @db.Text
  sourceSystem     String?
  correlationId    String?
  triggerCondition String?
  severity         String?
  autoResolve      Boolean  @default(false)
  reminderInterval Int?
  escalationLevel  Int?     @default(1)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  alert Alert @relation(fields: [alertId], references: [id], onDelete: Cascade)

  @@index([alertId])
  @@index([entityType, entityId])
}

// Replace AuditLog.oldValues and newValues (Json?)
model AuditLogValues {
  id         String   @id @default(uuid()) @db.Uuid
  auditLogId String   @db.Uuid
  valueType  String // 'old' or 'new'
  fieldName  String
  fieldValue String?  @db.Text
  dataType   String? // 'string', 'number', 'boolean', 'date', 'object'
  createdAt  DateTime @default(now())

  auditLog AuditLog @relation(fields: [auditLogId], references: [id], onDelete: Cascade)

  @@index([auditLogId])
  @@index([valueType])
  @@index([fieldName])
}

// Replace Employer.address (Json?)
model EmployerAddress {
  id          String   @id @default(uuid()) @db.Uuid
  employerId  String   @unique @db.Uuid
  street      String?
  houseNumber String?
  apartment   String?
  city        String?
  postalCode  String?
  country     String?  @default("Israel")
  region      String?
  district    String?
  coordinates String?
  isMain      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  employer Employer @relation(fields: [employerId], references: [id], onDelete: Cascade)

  @@index([employerId])
  @@index([city])
  @@index([postalCode])
}

// Replace Employer.contact (Json?)
model EmployerContact {
  id            String   @id @default(uuid()) @db.Uuid
  employerId    String   @unique @db.Uuid
  primaryPhone  String?
  mobilePhone   String?
  faxNumber     String?
  email         String?
  website       String?
  contactPerson String?
  position      String?
  department    String?
  notes         String?  @db.Text
  isMain        Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  employer Employer @relation(fields: [employerId], references: [id], onDelete: Cascade)

  @@index([employerId])
  @@index([email])
  @@index([primaryPhone])
}

// Replace Employee.address (Json?)
model EmployeeAddress {
  id          String   @id @default(uuid()) @db.Uuid
  employeeId  String   @unique @db.Uuid
  street      String?
  houseNumber String?
  apartment   String?
  city        String?
  postalCode  String?
  country     String?  @default("Israel")
  region      String?
  district    String?
  coordinates String?
  isMain      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@index([employeeId])
  @@index([city])
  @@index([postalCode])
}

// Replace Employee.contact (Json?)
model EmployeeContact {
  id                String   @id @default(uuid()) @db.Uuid
  employeeId        String   @unique @db.Uuid
  primaryPhone      String?
  mobilePhone       String?
  homePhone         String?
  email             String?
  emergencyContact  String?
  emergencyPhone    String?
  emergencyRelation String?
  preferredMethod   String? // 'email', 'sms', 'phone'
  notes             String?  @db.Text
  isMain            Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@index([employeeId])
  @@index([email])
  @@index([mobilePhone])
  @@index([emergencyPhone])
}

// Replace Employee.assignments (Json?)
model EmployeeAssignment {
  id                 String    @id @default(uuid()) @db.Uuid
  employeeId         String    @db.Uuid
  assignmentType     String // 'primary', 'secondary', 'temporary'
  title              String?
  description        String?   @db.Text
  startDate          DateTime
  endDate            DateTime?
  percentage         Decimal?  @db.Decimal(5, 2)
  hourlyRate         Decimal?  @db.Decimal(12, 2)
  responsibilityCode String?
  departmentCode     String?
  locationCode       String?
  supervisorId       String?   @db.Uuid
  notes              String?   @db.Text
  isActive           Boolean   @default(true)
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  employee Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)

  @@index([employeeId])
  @@index([assignmentType])
  @@index([startDate, endDate])
  @@index([isActive])
}

// Replace Payslip.breakdown (Json?)
model PayslipBreakdown {
  id              String   @id @default(uuid()) @db.Uuid
  payslipId       String   @db.Uuid
  category        String // 'gross', 'deductions', 'contributions', 'net'
  subcategory     String?
  description     String
  amount          Decimal  @db.Decimal(12, 2)
  percentage      Decimal? @db.Decimal(5, 2)
  calculationBase Decimal? @db.Decimal(12, 2)
  rate            Decimal? @db.Decimal(12, 2)
  units           Decimal? @db.Decimal(12, 2)
  componentCode   String?
  isStatutory     Boolean  @default(false)
  displayOrder    Int?
  notes           String?
  createdAt       DateTime @default(now())

  payslip Payslip @relation(fields: [payslipId], references: [id], onDelete: Cascade)

  @@index([payslipId])
  @@index([category])
  @@index([componentCode])
  @@index([displayOrder])
}

// Replace Form102.data, Form106.data, Form126.data (Json)
model FormData {
  id             String   @id @default(uuid()) @db.Uuid
  formType       String // 'form102', 'form106', 'form126'
  formId         String   @db.Uuid
  fieldName      String
  fieldValue     String?  @db.Text
  dataType       String? // 'string', 'number', 'boolean', 'date'
  section        String?
  subsection     String?
  displayOrder   Int?
  isRequired     Boolean  @default(false)
  isCalculated   Boolean  @default(false)
  formula        String?  @db.Text
  validationRule String?  @db.Text
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@index([formType, formId])
  @@index([fieldName])
  @@index([section, subsection])
  @@index([displayOrder])
}

// Replace Report.parameters (Json?)
model ReportParameters {
  id              String    @id @default(uuid()) @db.Uuid
  reportId        String    @unique @db.Uuid
  dateFrom        DateTime?
  dateTo          DateTime?
  employeeIds     String?   @db.Text // Comma-separated IDs
  departmentIds   String?   @db.Text // Comma-separated IDs
  includeInactive Boolean   @default(false)
  groupBy         String?
  sortBy          String?
  sortDirection   String?   @default("ASC")
  filterCriteria  String?   @db.Text
  customFields    String?   @db.Text
  outputFormat    String?   @default("PDF")
  pageSize        String?   @default("A4")
  orientation     String?   @default("portrait")
  includeCharts   Boolean   @default(true)
  includeSummary  Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  report Report @relation(fields: [reportId], references: [id], onDelete: Cascade)

  @@index([reportId])
}

// Replace Document.metadata (Json?)
model DocumentMetadata {
  id              String    @id @default(uuid()) @db.Uuid
  documentId      String    @unique @db.Uuid
  originalName    String?
  fileExtension   String?
  compressionType String?
  checksum        String?
  ocrText         String?   @db.Text
  extractedData   String?   @db.Text
  tags            String?   @db.Text // Comma-separated tags
  keywords        String?   @db.Text // Comma-separated keywords
  expiryDate      DateTime?
  reminderDate    DateTime?
  isEncrypted     Boolean   @default(false)
  encryptionKey   String?
  accessLevel     String?   @default("private")
  documentVersion String?   @default("1.0")
  approvalStatus  String?
  approvedBy      String?   @db.Uuid
  approvedAt      DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  document Document @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@index([documentId])
  @@index([tags])
  @@index([expiryDate])
  @@index([approvalStatus])
}

// Replace ImportExportHistory.details (Json?)
model ImportExportDetails {
  id                String   @id @default(uuid()) @db.Uuid
  importExportId    String   @unique @db.Uuid
  sourceFile        String?
  targetFile        String?
  mappingConfig     String?  @db.Text
  validationRules   String?  @db.Text
  errorMessages     String?  @db.Text
  warningMessages   String?  @db.Text
  recordsProcessed  Int?     @default(0)
  recordsSuccessful Int?     @default(0)
  recordsFailed     Int?     @default(0)
  processingTime    Int? // in milliseconds
  memoryUsage       String?
  fileSize          Int? // in bytes
  compressionRatio  Decimal? @db.Decimal(5, 2)
  checksumBefore    String?
  checksumAfter     String?
  rollbackAvailable Boolean  @default(false)
  rollbackData      String?  @db.Text
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  importExport ImportExportHistory @relation(fields: [importExportId], references: [id], onDelete: Cascade)

  @@index([importExportId])
}

// Replace DocumentSettings.allowedCategories (Json)
model AllowedDocumentCategory {
  id               String   @id @default(uuid()) @db.Uuid
  settingsId       String   @db.Uuid // References DocumentSettings.tenantId
  categoryName     String
  description      String?
  isRequired       Boolean  @default(false)
  maxFileSize      Int? // in bytes
  allowedMimeTypes String?  @db.Text // Comma-separated
  retentionPeriod  Int? // in days
  isActive         Boolean  @default(true)
  displayOrder     Int?
  validationRules  String?  @db.Text
  accessRoles      String?  @db.Text // Comma-separated roles
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  settings DocumentSettings @relation(fields: [settingsId], references: [tenantId], onDelete: Cascade)

  @@unique([settingsId, categoryName])
  @@index([settingsId])
  @@index([categoryName])
  @@index([isActive])
}

// Replace SalaryTemplate.components (Json?)
model SalaryTemplateComponent {
  id              String    @id @default(uuid()) @db.Uuid
  templateId      String    @db.Uuid
  componentType   String // 'basic_salary', 'allowance', 'deduction', 'benefit'
  componentName   String
  componentCode   String?
  description     String?   @db.Text
  calculationType String // 'fixed', 'percentage', 'formula'
  fixedAmount     Decimal?  @db.Decimal(12, 2)
  percentage      Decimal?  @db.Decimal(5, 2)
  formula         String?   @db.Text
  basedOn         String? // What the calculation is based on
  isRequired      Boolean   @default(false)
  isVisible       Boolean   @default(true)
  displayOrder    Int?
  effectiveFrom   DateTime?
  effectiveTo     DateTime?
  conditions      String?   @db.Text
  notes           String?   @db.Text
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  template SalaryTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([templateId])
  @@index([componentType])
  @@index([componentCode])
  @@index([isActive])
  @@index([displayOrder])
}

// Replace SalaryAgreement.terms (Json?)
model SalaryAgreementTerm {
  id               String    @id @default(uuid()) @db.Uuid
  agreementId      String    @db.Uuid
  termType         String // 'salary', 'bonus', 'benefit', 'condition', 'clause'
  termName         String
  termCode         String?
  description      String?   @db.Text
  value            String?   @db.Text
  unit             String? // 'amount', 'percentage', 'hours', 'days'
  currency         String?   @default("ILS")
  frequency        String? // 'monthly', 'yearly', 'one-time'
  isNegotiable     Boolean   @default(false)
  isConditional    Boolean   @default(false)
  conditions       String?   @db.Text
  effectiveFrom    DateTime?
  effectiveTo      DateTime?
  approvalRequired Boolean   @default(false)
  approvedBy       String?   @db.Uuid
  approvedAt       DateTime?
  notes            String?   @db.Text
  displayOrder     Int?
  isActive         Boolean   @default(true)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  agreement SalaryAgreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)

  @@index([agreementId])
  @@index([termType])
  @@index([termCode])
  @@index([isActive])
  @@index([displayOrder])
}

// Replace Setting.value (Json)
model SettingValue {
  id           String    @id @default(uuid()) @db.Uuid
  settingId    String    @db.Uuid
  valueType    String // 'string', 'number', 'boolean', 'array', 'object'
  stringValue  String?   @db.Text
  numberValue  Decimal?  @db.Decimal(15, 6)
  booleanValue Boolean?
  dateValue    DateTime?
  arrayIndex   Int? // For array values
  objectKey    String? // For object values
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  setting Setting @relation(fields: [settingId], references: [id], onDelete: Cascade)

  @@index([settingId])
  @@index([valueType])
  @@index([objectKey])
}

enum Role {
  OWNER
  ADMIN
  ACCOUNTANT
  HR
  EMPLOYEE
}

enum Currency {
  ILS
  USD
  EUR
  OTHER
}

enum PayFrequency {
  MONTHLY
  BI_WEEKLY
  WEEKLY
}

enum Basis {
  MONTHLY
  HOURLY
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
}

enum AlertType {
  INFO
  WARNING
  CRITICAL
}

enum AlertCategory {
  VISA_EXPIRATION
  MISSING_FORM101
  MISSING_DOCUMENTS
  OVERTIME_LIMIT
  DEPOSIT_COMPLIANCE
  UNUSUAL_DEDUCTION
  GENERAL
}

enum EmployeeStatus {
  ACTIVE
  TERMINATED
  SUSPENDED
}

enum FundType {
  PENSION
  STUDY
  MANAGERS_INS
  COMPENSATION
  DEPOSIT
  HISHTALMUT
  UNION
}

enum PayslipItemType {
  EARNING
  DEDUCTION
  EMPLOYER_CONTRIB
  REIMBURSEMENT
}

/// ------------------------------------------------------------
/// Enum: PayslipItemKod
/// ערכי @map הם הערכים האמיתיים בבסיס-הנתונים (VARCHAR)
/// השמות עצמם צריכים להתחיל באות, לכן מוסיפים K_
/// ------------------------------------------------------------
enum PayslipItemKod {
  K_1000        @map("1000")
  K_1001        @map("1001")
  K_1002        @map("1002")
  K_1003        @map("1003")
  K_1004        @map("1004")
  K_1005        @map("1005")
  K_1008        @map("1008")
  K_1010        @map("1010")
  K_1011        @map("1011")
  K_1020        @map("1020")
  K_1021        @map("1021")
  K_1022        @map("1022")
  K_1063        @map("1063")
  K_1077        @map("1077")
  K_1089        @map("1089")
  K_1090        @map("1090")
  K_1091        @map("1091")
  K_0100        @map("0100")
  K_0102        @map("0102")
  K_0113        @map("0113")
  K_0114        @map("0114")
  K_0120        @map("0120")
  K_0131        @map("0131")
  K_0170        @map("0170")
  K_0191        @map("0191")
  K_0192        @map("0192")
  K_0193        @map("0193")
  K_0250        @map("0250")
  K_0312        @map("0312")
  K_0313        @map("0313")
  K_0400        @map("0400")
  K_0500        @map("0500")
  K_0900        @map("0900")
  K_0121        @map("0121")
  K_GILUM       @map("GILUM")
  K_TAX         @map("TAX")
  K_NI_EMP      @map("NI_EMP")
  K_NI_HEALTH   @map("NI_HEALTH")
  K_PENSION_EMP @map("PENSION_EMP")
  K_UNION       @map("UNION")
}

enum TransferAction {
  IMPORT
  EXPORT
}

enum SignatureRequestStatus {
  PENDING
  SIGNED
  EXPIRED
  CANCELLED
}

enum Form101Status {
  DRAFT
  PENDING_SIGNATURE
  SIGNED
  SUBMITTED
}

enum AccountType {
  CHECKING
  SAVINGS
  CREDIT
  LOAN
}

enum ReportType {
  FORM_102
  PAYROLL_SUMMARY
  TAX_REPORT
  EMPLOYEE_OVERVIEW
}

enum PositionType {
  MONTHLY
  HOURLY
  DAILY
  FINAL_PAY
}

enum LeaveType {
  VACATION
  SICK
  UNPAID
  MATERNITY
  MILITARY
  INTER_VISA
  OTHER
}

enum AgreementType {
  PERSONAL
  COLLECTIVE
  TEMPLATE
}

enum Sector {
  AGRICULTURE
  CONSTRUCTION
  CAREGIVING
  INDUSTRY
  HOSPITALITY
  TECHNOLOGY
  OTHER
}

enum MaritalStatus {
  SINGLE
  MARRIED
  DIVORCED
  WIDOWED
}

enum PayslipStatus {
  DRAFT
  CALCULATED
  APPROVED
  SENT
  PAID
  CANCELLED
}

model EmployeeRole {
  id           String        @id @default(uuid()) @db.Uuid
  tenantId     String        @db.Uuid
  employerId   String        @db.Uuid
  name         String
  description  String?
  isActive     Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer     Employer      @relation(fields: [employerId], references: [id], onDelete: Cascade)
  associations Association[]

  @@unique([employerId, name])
  @@index([tenantId])
  @@index([employerId])
}

model SalaryTemplate {
  id                      String                    @id @default(uuid()) @db.Uuid
  tenantId                String                    @db.Uuid
  employerId              String                    @db.Uuid
  name                    String
  description             String?
  components              Json? // For storing salary components structure
  isActive                Boolean                   @default(true)
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @updatedAt
  tenant                  Tenant                    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer                Employer                  @relation(fields: [employerId], references: [id], onDelete: Cascade)
  SalaryTemplateComponent SalaryTemplateComponent[]

  @@unique([employerId, name])
  @@index([tenantId])
  @@index([employerId])
}

model SalaryAgreement {
  id                  String                @id @default(uuid()) @db.Uuid
  tenantId            String                @db.Uuid
  employerId          String                @db.Uuid
  name                String
  description         String?
  effectiveFrom       DateTime?
  effectiveTo         DateTime?
  terms               Json? // For storing agreement terms
  isActive            Boolean               @default(true)
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  tenant              Tenant                @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer            Employer              @relation(fields: [employerId], references: [id], onDelete: Cascade)
  SalaryAgreementTerm SalaryAgreementTerm[]

  @@unique([employerId, name])
  @@index([tenantId])
  @@index([employerId])
}

model Association {
  id                String           @id @default(uuid()) @db.Uuid
  tenantId          String           @db.Uuid
  employeeId        String           @db.Uuid
  associationType   AssociationType?
  entityId          String?          @db.Uuid
  entityName        String?
  departmentId      String?          @db.Uuid
  roleId            String?          @db.Uuid
  salaryTemplateId  String?          @db.Uuid
  salaryAgreementId String?          @db.Uuid
  startDate         DateTime
  endDate           DateTime?
  notes             String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  deletedAt         DateTime?
  employee          Employee         @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant            Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  department        Department?      @relation(fields: [departmentId], references: [id])
  role              EmployeeRole?    @relation(fields: [roleId], references: [id])

  @@index([tenantId])
  @@index([employeeId])
  @@index([entityId])
  @@index([associationType])
  @@index([departmentId])
  @@index([roleId])
  @@index([salaryTemplateId])
  @@index([salaryAgreementId])
  @@index([startDate, endDate])
  @@index([deletedAt])
}

// Attendance Agreement Models
model AttendanceAgreement {
  id                 String              @id @default(uuid()) @db.Uuid
  tenantId           String              @db.Uuid
  code               String
  name               String
  description        String?
  status             AgreementStatus     @default(ACTIVE)
  workDaysPerWeek    Int                 @default(5)
  hoursPerDay        Float               @default(8)
  monthlyHours       Float               @default(173)
  overtimeThreshold  Float               @default(8)
  nightShiftStart    String?
  nightShiftEnd      String?
  weekendDays        Int[]               @default([5, 6])
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  deletedAt          DateTime?
  tenant             Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employeeAgreements EmployeeAgreement[]
  shifts             Shift[]
  overtimeRules      OvertimeRule[]
  breakRules         BreakRule[]

  @@unique([tenantId, code])
  @@index([tenantId])
  @@index([status])
  @@index([deletedAt])
}

model EmployeeAgreement {
  id          String              @id @default(uuid()) @db.Uuid
  tenantId    String              @db.Uuid
  employeeId  String              @db.Uuid
  agreementId String              @db.Uuid
  startDate   DateTime
  endDate     DateTime?
  notes       String?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  deletedAt   DateTime?
  tenant      Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employee    Employee            @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  agreement   AttendanceAgreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([agreementId])
  @@index([startDate, endDate])
  @@index([deletedAt])
}

model Shift {
  id           String              @id @default(uuid()) @db.Uuid
  tenantId     String              @db.Uuid
  agreementId  String              @db.Uuid
  code         String
  name         String
  description  String?
  startTime    String
  endTime      String
  breakMinutes Int                 @default(0)
  isNightShift Boolean             @default(false)
  isFlexible   Boolean             @default(false)
  color        String?
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt
  deletedAt    DateTime?
  tenant       Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  agreement    AttendanceAgreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)

  @@unique([agreementId, code])
  @@index([tenantId])
  @@index([agreementId])
  @@index([deletedAt])
}

model OvertimeRule {
  id          String              @id @default(uuid()) @db.Uuid
  tenantId    String              @db.Uuid
  agreementId String              @db.Uuid
  name        String
  description String?
  fromHour    Float
  toHour      Float?
  rate        Float               @default(1.25)
  dayType     DayType             @default(WEEKDAY)
  priority    Int                 @default(0)
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  deletedAt   DateTime?
  tenant      Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  agreement   AttendanceAgreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([agreementId])
  @@index([dayType])
  @@index([priority])
  @@index([deletedAt])
}

model BreakRule {
  id               String              @id @default(uuid()) @db.Uuid
  tenantId         String              @db.Uuid
  agreementId      String              @db.Uuid
  name             String
  description      String?
  minWorkHours     Float
  breakDuration    Int // in minutes
  isPaid           Boolean             @default(false)
  isMandatory      Boolean             @default(true)
  canBeSplit       Boolean             @default(false)
  minSplitDuration Int? // in minutes
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  deletedAt        DateTime?
  tenant           Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  agreement        AttendanceAgreement @relation(fields: [agreementId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([agreementId])
  @@index([minWorkHours])
  @@index([deletedAt])
}

model Location {
  id          String     @id @default(uuid()) @db.Uuid
  tenantId    String     @db.Uuid
  code        String
  name        String
  description String?
  address     String?
  latitude    Float?
  longitude   Float?
  radius      Int? // in meters for geofencing
  timezone    String     @default("Asia/Jerusalem")
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  deletedAt   DateTime?
  tenant      Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  movements   Movement[]

  @@unique([tenantId, code])
  @@index([tenantId])
  @@index([isActive])
  @@index([deletedAt])
}

model MovementType {
  id               String           @id @default(uuid()) @db.Uuid
  tenantId         String           @db.Uuid
  code             String
  name             String
  description      String?
  category         MovementCategory
  isDefault        Boolean          @default(false)
  requiresApproval Boolean          @default(false)
  color            String?
  icon             String?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  deletedAt        DateTime?
  tenant           Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  movements        Movement[]

  @@unique([tenantId, code])
  @@index([tenantId])
  @@index([category])
  @@index([isDefault])
  @@index([deletedAt])
}

model Movement {
  id         String         @id @default(uuid()) @db.Uuid
  tenantId   String         @db.Uuid
  employeeId String         @db.Uuid
  typeId     String         @db.Uuid
  locationId String?        @db.Uuid
  timestamp  DateTime
  source     MovementSource @default(MANUAL)
  ipAddress  String?
  deviceId   String?
  notes      String?
  isApproved Boolean        @default(true)
  approvedBy String?        @db.Uuid
  approvedAt DateTime?
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  deletedAt  DateTime?
  tenant     Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employee   Employee       @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  type       MovementType   @relation(fields: [typeId], references: [id])
  location   Location?      @relation(fields: [locationId], references: [id])

  @@index([tenantId])
  @@index([employeeId])
  @@index([typeId])
  @@index([locationId])
  @@index([timestamp])
  @@index([source])
  @@index([deletedAt])
}

// ============================================
// Deduction Components
// ============================================

model DeductionComponent {
  id                        String                        @id @default(uuid()) @db.Uuid
  tenantId                  String                        @db.Uuid
  employerId                String?                       @db.Uuid
  code                      String
  name                      String
  description               String?
  deductionType             DeductionType
  taxCalculation            TaxCalculationType
  socialSecurityCalculation SocialSecurityCalculationType
  affectsPension            Boolean                       @default(false)
  percentageOfSalary        Decimal?                      @db.Decimal(5, 2)
  group                     DeductionGroup?
  isOneTime                 Boolean                       @default(false)
  isActive                  Boolean                       @default(true)
  createdAt                 DateTime                      @default(now())
  updatedAt                 DateTime                      @updatedAt
  deletedAt                 DateTime?

  // Relations
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer     Employer?     @relation(fields: [employerId], references: [id], onDelete: Cascade)
  payslipItems PayslipItem[]

  @@unique([tenantId, code])
  @@index([tenantId, employerId, deductionType, group, isActive, deletedAt])
}

enum AssociationType {
  COMPANY
  DEPARTMENT
  ROLE
  SITE
  SALARY_TEMPLATE
  SALARY_AGREEMENT
}

enum AgreementStatus {
  ACTIVE
  INACTIVE
  DRAFT
}

enum DayType {
  WEEKDAY
  WEEKEND
  HOLIDAY
}

enum MovementCategory {
  CHECK_IN
  CHECK_OUT
  BREAK_START
  BREAK_END
  OTHER
}

enum MovementSource {
  MANUAL
  MOBILE_APP
  WEB_APP
  BIOMETRIC
  CARD_READER
  API
}

enum DeductionType {
  INCOME_TAX
  NATIONAL_INSURANCE
  HEALTH_INSURANCE
  PENSION
  EDUCATION_FUND
  LOAN_REPAYMENT
  OTHER
}

enum TaxCalculationType {
  TAX_LIABLE
  TAX_EXEMPT
}

enum SocialSecurityCalculationType {
  SS_LIABLE
  SS_EXEMPT
}

enum DeductionGroup {
  MANDATORY
  VOLUNTARY
  OTHER
}

enum PaymentType {
  ALLOWANCE
  EXPENSE
  REIMBURSEMENT
  CASH_REDEMPTION
  OVERTIME
  OTHER
}

enum PaymentGroup {
  BASIC
  SUPPLEMENT
}

enum ValueComponentType {
  VEHICLE
  PHONE
  MEAL
  INTEREST
  OTHER
}

enum FormulaType {
  TAX
  DEDUCTION
  PENSION
  BENEFIT
  OTHER
}

enum FormulaStatus {
  ACTIVE
  INACTIVE
  DRAFT
}

model Formula {
  id          String        @id @default(uuid()) @db.Uuid
  tenantId    String        @db.Uuid
  employerId  String        @db.Uuid
  name        String
  description String?
  type        FormulaType
  startDate   DateTime
  endDate     DateTime?
  status      FormulaStatus @default(DRAFT)
  formulaCode String        @db.Text
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  tenant   Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer Employer @relation(fields: [employerId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employerId])
  @@index([type])
  @@index([status])
}

model ValueComponent {
  id                        String                         @id @default(uuid()) @db.Uuid
  tenantId                  String                         @db.Uuid
  employerId                String?                        @db.Uuid
  code                      String
  name                      String
  description               String?
  valueComponentType        ValueComponentType
  taxCalculation            TaxCalculationType?
  socialSecurityCalculation SocialSecurityCalculationType?
  affectsPension            Boolean                        @default(false)
  isOneTime                 Boolean                        @default(false)
  isActive                  Boolean                        @default(true)
  createdAt                 DateTime                       @default(now())
  updatedAt                 DateTime                       @updatedAt
  deletedAt                 DateTime?

  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer Employer? @relation(fields: [employerId], references: [id], onDelete: Cascade)

  @@unique([tenantId, code])
  @@index([tenantId, employerId, valueComponentType, isActive, deletedAt])
}

model Setting {
  id          String   @id @default(uuid()) @db.Uuid
  tenantId    String   @db.Uuid
  key         String
  value       Json
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  tenant       Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  SettingValue SettingValue[]

  @@unique([tenantId, key])
  @@index([tenantId])
}

model PaymentComponent {
  id                        String                         @id @default(uuid()) @db.Uuid
  tenantId                  String                         @db.Uuid
  employerId                String?                        @db.Uuid
  code                      String
  name                      String
  description               String?
  paymentType               PaymentType // Enum חדש
  paymentGroup              PaymentGroup? // Enum חדש
  taxCalculation            TaxCalculationType?
  socialSecurityCalculation SocialSecurityCalculationType?
  affectsPension            Boolean                        @default(false)
  isOneTime                 Boolean                        @default(false)
  isActive                  Boolean                        @default(true)
  createdAt                 DateTime                       @default(now())
  updatedAt                 DateTime                       @updatedAt
  deletedAt                 DateTime?

  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  employer Employer? @relation(fields: [employerId], references: [id], onDelete: Cascade)
  payslipItems PayslipItem[]

  @@unique([tenantId, employerId, code]) // Ensuring uniqueness includes employerId if present
  @@index([tenantId, employerId, paymentType, paymentGroup, isActive, deletedAt])
}

model SmsLog {
  id              String   @id @default(uuid()) @db.Uuid
  tenantId        String   @db.Uuid
  employeeId      String   @db.Uuid
  phone           String
  message         String
  status          Boolean
  failureReason   String?
  gatewayResponse String?
  httpStatus      Int?
  sentAt          DateTime @default(now())
  employee        Employee @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  tenant          Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([employeeId])
  @@index([sentAt])
  @@index([status])
}

model SignatureRequest {
  id            String                 @id @default(uuid()) @db.Uuid
  tenantId      String                 @db.Uuid
  form101Id     String                 @db.Uuid
  status        SignatureRequestStatus @default(PENDING)
  expiresAt     DateTime
  signedAt      DateTime?
  signatureData String? // Base64 encoded signature image
  createdAt     DateTime               @default(now())
  updatedAt     DateTime               @updatedAt
  form101       Form101                @relation(fields: [form101Id], references: [id], onDelete: Cascade)
  tenant        Tenant                 @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([form101Id])
  @@index([expiresAt])
}

model TaxBracket {
  id        String  @id @default(uuid()) @db.Uuid
  tenantId  String  @db.Uuid
  year      Int
  minIncome Decimal @db.Decimal(12, 2)
  maxIncome Decimal @db.Decimal(12, 2)
  rate      Decimal @db.Decimal(5, 2)
  tenant    Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([tenantId, year])
}

model NationalInsuranceRule {
  id           String  @id @default(uuid()) @db.Uuid
  tenantId     String  @db.Uuid
  rateEmployee Decimal @db.Decimal(5, 2)
  rateEmployer Decimal @db.Decimal(5, 2)
  threshold    Decimal @db.Decimal(12, 2)
  tenant       Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
}

model SalaryComponent {
  id           String  @id @default(uuid()) @db.Uuid
  tenantId     String  @db.Uuid
  name         String
  type         String
  defaultValue Decimal @db.Decimal(12, 2)
  tenant       Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
}

model SalaryRule {
  id              String  @id @default(uuid()) @db.Uuid
  tenantId        String  @db.Uuid
  name            String
  calculationType String
  value           Decimal @db.Decimal(12, 2)
  tenant          Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
}

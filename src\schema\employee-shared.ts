import { z } from "zod";

// Employee status enum
export const EmployeeStatus = z.enum(["פעיל", "חופשה", "מושעה", "לא פעיל"]);
export type EmployeeStatus = z.infer<typeof EmployeeStatus>;

// Employee schema
export const employeeSchema = z.object({
  id: z.string(),
  name: z.string().min(2).max(100),
  email: z.string().email(),
  phone: z.string().optional(),
  position: z.string(),
  department: z.string(),
  startDate: z.string(), // Date in format DD/MM/YYYY
  status: EmployeeStatus,
  salary: z.string(), // Formatted salary with currency symbol
  manager: z.string().optional(),
  location: z.string().optional(),
  employerId: z.string(),
});

// Schema for creating a new employee
export const createEmployeeSchema = employeeSchema
  .omit({ id: true })
  .extend({
    email: z.string().email(),
    phone: z.string().min(9).max(15),
  });

// Schema for updating an employee
export const updateEmployeeSchema = createEmployeeSchema
  .partial()
  .extend({ id: z.string() });

// Schema for filtering employees
export const employeeFilterSchema = z.object({
  status: EmployeeStatus.optional(),
  department: z.string().optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(50),
  cursor: z.string().optional(),
});

// Department schema
export const departmentSchema = z.object({
  id: z.string(),
  name: z.string().min(2).max(100),
  description: z.string().optional(),
  employeeCount: z.number(),
  manager: z.string().optional(),
  employerId: z.string(),
});

// Schema for creating a department
export const createDepartmentSchema = departmentSchema
  .omit({ id: true, employeeCount: true })
  .extend({
    description: z.string().optional(),
  });

// Schema for updating a department
export const updateDepartmentSchema = createDepartmentSchema
  .partial()
  .extend({ id: z.string() });

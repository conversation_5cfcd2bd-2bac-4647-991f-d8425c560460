import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { Prisma } from "@prisma/client";
import { Role } from "@prisma/client";

// Input validation schemas
const createEmployeeAgreementSchema = z.object({
  employeeId: z.string(),
  agreementId: z.string(),
  startDate: z.date(),
  endDate: z.date().optional(),
  notes: z.string().optional(),
});

const updateEmployeeAgreementSchema = createEmployeeAgreementSchema.extend({
  id: z.string(),
});

export const employeeAgreementRouter = createTRPCRouter({
  // Get all employee agreements
  getAll: publicProcedure
    .input(
      z.object({
        employeeId: z.string().optional(),
        agreementId: z.string().optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where: Prisma.EmployeeAgreementWhereInput = { deletedAt: null };
      if (input?.employeeId) {
        where.employeeId = input.employeeId;
      }
      if (input?.agreementId) {
        where.agreementId = input.agreementId;
      }
      
      return ctx.db.employeeAgreement.findMany({
        where,
        include: {
          employee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              nationalId: true,
            },
          },
          agreement: true,
        },
        orderBy: {
          startDate: "desc",
        },
      });
    }),

  // Get employee agreement by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const agreement = await ctx.db.employeeAgreement.findUnique({
        where: { id: input.id },
        include: {
          employee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              nationalId: true,
            },
          },
          agreement: true,
        },
      });

      if (!agreement || agreement.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "שיוך הסכם לעובד לא נמצא",
        });
      }

      return agreement;
    }),
  // Create new employee agreement
  create: protectedProcedure
    .input(createEmployeeAgreementSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if employee exists
      const employee = await ctx.db.employee.findUnique({
        where: { id: input.employeeId },
      });

      if (!employee) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "עובד לא נמצא",
        });
      }

      // Check if agreement exists
      const agreement = await ctx.db.attendanceAgreement.findUnique({
        where: { id: input.agreementId },
      });

      if (!agreement || agreement.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "הסכם נוכחות לא נמצא",
        });
      }

      // Check for overlapping agreements
      const overlapping = await ctx.db.employeeAgreement.findFirst({
        where: {
          employeeId: input.employeeId,
          deletedAt: null,
          OR: [
            // New agreement starts during existing agreement
            {
              startDate: { lte: input.startDate },
              OR: [
                { endDate: null },
                { endDate: { gte: input.startDate } },
              ],
            },
            // New agreement ends during existing agreement
            input.endDate ? {
              startDate: { lte: input.endDate },
              OR: [
                { endDate: null },
                { endDate: { gte: input.endDate } },
              ],
            } : {},
            // New agreement encompasses existing agreement
            {
              startDate: { gte: input.startDate },
              OR: input.endDate ? [
                { endDate: { lte: input.endDate } },
                { endDate: null },
              ] : [],
            },
          ],
        },
      });

      if (overlapping) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קיים הסכם חופף לעובד זה בתקופה המבוקשת",
        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.employeeAgreement.create({
        data: {
          ...input,
          tenantId,
        },
        include: {
          employee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              nationalId: true,
            },
          },
          agreement: true,
        },
      });
    }),

  // Update employee agreement
  update: protectedProcedure
    .input(updateEmployeeAgreementSchema)
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const { id, ...data } = input;

      // Check if agreement exists
      const existing = await ctx.db.employeeAgreement.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "שיוך הסכם לעובד לא נמצא",
        });
      }

      // Check for overlapping agreements (excluding current)
      const overlapping = await ctx.db.employeeAgreement.findFirst({
        where: {
          employeeId: data.employeeId,
          id: { not: id },
          deletedAt: null,
          OR: [
            // Updated agreement starts during existing agreement
            {
              startDate: { lte: data.startDate },
              OR: [
                { endDate: null },
                { endDate: { gte: data.startDate } },
              ],
            },
            // Updated agreement ends during existing agreement
            data.endDate ? {
              startDate: { lte: data.endDate },
              OR: [
                { endDate: null },
                { endDate: { gte: data.endDate } },
              ],
            } : {},
            // Updated agreement encompasses existing agreement
            {
              startDate: { gte: data.startDate },
              OR: data.endDate ? [
                { endDate: { lte: data.endDate } },
                { endDate: null },
              ] : [],
            },
          ],
        },
      });

      if (overlapping) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קיים הסכם חופף לעובד זה בתקופה המבוקשת",
        });
      }

      return ctx.db.employeeAgreement.update({
        where: { id },
        data,
        include: {
          employee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              nationalId: true,
            },
          },
          agreement: true,
        },
      });
    }),

  // Delete employee agreement (soft delete)
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      // Check if agreement exists
      const existing = await ctx.db.employeeAgreement.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "שיוך הסכם לעובד לא נמצא",
        });
      }

      return ctx.db.employeeAgreement.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
}); 
import { createTRPCRouter, protectedProcedure, tenantProcedure } from "@/server/api/trpc";
import { calculatePayslip, recalculatePayslip } from "@/server/services/payslip";
import { z } from "zod";
import { PayslipStatus } from "@prisma/client";
import type { Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";

export const payslipRouter = createTRPCRouter({
	calculate: protectedProcedure
		.input(
			z.object({
				employeeId: z.string(),
				month: z.number().min(1).max(12),
				year: z.number().min(1900),
				workHours: z.object({
					regularHours: z.number().optional(),
					overtime125: z.number().optional(),
					overtime150: z.number().optional(),
					overtime175: z.number().optional(),
					overtime200: z.number().optional(),
				}).optional(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const payslip = await calculatePayslip(ctx.db, {
				employeeId: input.employeeId,
				month: input.month,
				year: input.year,
			}, input.workHours);
			return payslip;
		}),

	getAll: protectedProcedure
		.input(
			z.object({
				employeeId: z.string().optional(),
				year: z.number().optional(),
				month: z.number().optional(),
				status: z.nativeEnum(PayslipStatus).optional(),
				limit: z.number().min(1).max(100).default(50),
				offset: z.number().default(0),
			}),
		)
                .query(async ({ ctx, input }) => {
                        const where: Prisma.PayslipWhereInput = {
                                tenantId: ctx.tenantId,
                        };

			if (input.employeeId) where.employeeId = input.employeeId;
			if (input.year) where.year = input.year;
			if (input.month) where.month = input.month;
			if (input.status) where.status = input.status;

			const [payslips, total] = await Promise.all([
				ctx.db.payslip.findMany({
					where,
					include: {
						employee: {
							select: {
								id: true,
								firstName: true,
								lastName: true,
								nationalId: true,
							},
						},
						items: true,
					},
					orderBy: [
						{ year: 'desc' },
						{ month: 'desc' },
						{ employee: { lastName: 'asc' } },
					],
					take: input.limit,
					skip: input.offset,
				}),
				ctx.db.payslip.count({ where }),
			]);

			return {
				payslips,
				total,
				hasMore: input.offset + input.limit < total,
			};
		}),

        getById: protectedProcedure
                .input(z.string())
                .query(async ({ ctx, input }) => {
                        const payslip = await ctx.db.payslip.findUnique({
                                where: {
                                        id: input,
                                        tenantId: ctx.tenantId,
                                },
                                include: {
                                        employee: {
                                                select: {
                                                        id: true,
                                                        firstName: true,
                                                        lastName: true,
                                                        nationalId: true,
                                                },
                                        },
                                        items: {
                                                include: {
                                                        paymentComponent: {
                                                                select: {
                                                                        id: true,
                                                                        name: true,
                                                                        code: true,
                                                                        paymentType: true,
                                                                }
                                                        },
                                                        deductionComponent: {
                                                                select: {
                                                                        id: true,
                                                                        name: true,
                                                                        code: true,
                                                                        deductionType: true,
                                                                }
                                                        }
                                                }
                                        },
                                        contributions: true,
                                },
                        });

                        if (!payslip) {
                                throw new TRPCError({
                                        code: "NOT_FOUND",
                                        message: "תלוש שכר לא נמצא",
                                });
                        }

                        return payslip;
                }),

        update: tenantProcedure
		.input(
			z.object({
				id: z.string(),
				grossPay: z.number().optional(),
				netPay: z.number().optional(),
				taxDeducted: z.number().optional(),
				insuranceDeducted: z.number().optional(),
				otherDeductions: z.number().nullable().optional(),
				allowances: z.number().nullable().optional(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
                        const { id, ...data } = input;

                        const existing = await ctx.db.payslip.findUnique({
                                where: { id, tenantId: ctx.tenantId },
                        });

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status !== 'DRAFT' && existing.status !== 'CALCULATED') {
				throw new Error("לא ניתן לערוך תלוש שכר שכבר אושר או שולם");
			}

			// Convert numbers to Prisma Decimal
			const updateData: any = {};
			if (data.grossPay !== undefined) updateData.grossPay = data.grossPay;
			if (data.netPay !== undefined) updateData.netPay = data.netPay;
			if (data.taxDeducted !== undefined) updateData.taxDeducted = data.taxDeducted;
			if (data.insuranceDeducted !== undefined) updateData.insuranceDeducted = data.insuranceDeducted;
			if (data.otherDeductions !== undefined) updateData.otherDeductions = data.otherDeductions;
			if (data.allowances !== undefined) updateData.allowances = data.allowances;

			const updated = await ctx.db.payslip.update({
				where: { id },
				data: updateData,
				include: {
					employee: true,
					items: {
						include: {
							paymentComponent: {
								select: {
									id: true,
									name: true,
									code: true,
									paymentType: true,
								}
							},
							deductionComponent: {
								select: {
									id: true,
									name: true,
									code: true,
									deductionType: true,
								}
							}
						}
					},
				},
			});

			return updated;
		}),

        approve: tenantProcedure
                .input(z.string())
                .mutation(async ({ ctx, input }) => {
                        const existing = await ctx.db.payslip.findUnique({
                                where: {
                                        id: input,
                                        tenantId: ctx.tenantId,
                                },
                        });

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status !== 'CALCULATED') {
				throw new Error("ניתן לאשר רק תלוש שכר במצב מחושב");
			}

			const updated = await ctx.db.payslip.update({
				where: { id: input },
				data: {
					status: 'APPROVED',
					approvedAt: new Date(),
				},
				include: {
					employee: true,
					items: true,
				},
			});

			return updated;
		}),

        markAsPaid: tenantProcedure
                .input(z.string())
                .mutation(async ({ ctx, input }) => {
                        const existing = await ctx.db.payslip.findUnique({
                                where: {
                                        id: input,
                                        tenantId: ctx.tenantId,
                                },
                        });

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			if (existing.status !== 'APPROVED' && existing.status !== 'SENT') {
				throw new Error("ניתן לסמן כשולם רק תלוש שכר מאושר");
			}

			const updated = await ctx.db.payslip.update({
				where: { id: input },
				data: {
					status: 'PAID',
				},
				include: {
					employee: true,
					items: true,
				},
			});

			return updated;
		}),

	approveAndPay: tenantProcedure
		.input(z.string())
		.mutation(async ({ ctx, input }) => {
			const existing = await ctx.db.payslip.findUnique({
				where: {
					id: input,
					tenantId: ctx.tenantId,
				},
			});

			if (!existing) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "תלוש שכר לא נמצא",
				});
			}

			if (existing.status !== 'CALCULATED' && existing.status !== 'DRAFT') {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "ניתן לאשר ולשלם רק תלוש שכר במצב טיוטה או מחושב",
				});
			}

			const updated = await ctx.db.payslip.update({
				where: { id: input },
				data: {
					status: 'PAID',
					approvedAt: new Date(),
					paidAt: new Date(),
				},
				include: {
					employee: true,
					items: true,
				},
			});

			return updated;
		}),

	recalculate: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				workHours: z.object({
					regularHours: z.number().optional(),
					overtime125: z.number().optional(),
					overtime150: z.number().optional(),
					overtime175: z.number().optional(),
					overtime200: z.number().optional(),
				}).optional(),
			}),
		)
		.mutation(async ({ ctx, input }) => {
			const payslip = await recalculatePayslip(ctx.db, input.id, input.workHours);
			return payslip;
		}),

        delete: tenantProcedure
                .input(z.string())
                .mutation(async ({ ctx, input }) => {
                        const existing = await ctx.db.payslip.findUnique({
                                where: {
                                        id: input,
                                        tenantId: ctx.tenantId,
                                },
                        });

			if (!existing) {
				throw new Error("תלוש שכר לא נמצא");
			}

			// Only allow deletion of draft payslips
			if (existing.status !== 'DRAFT') {
				throw new Error("ניתן למחוק רק תלוש שכר במצב טיוטה");
			}

			// Delete all related items first
			await ctx.db.payslipItem.deleteMany({
				where: { payslipId: input },
			});

			// Delete the payslip
			await ctx.db.payslip.delete({
				where: { id: input },
			});

			return { success: true };
		}),

	// Get related PaymentComponents and DeductionComponents
	getSalaryComponents: tenantProcedure
		.query(async ({ ctx }) => {
			const [paymentComponents, deductionComponents] = await Promise.all([
				ctx.db.paymentComponent.findMany({
					where: {
						tenantId: ctx.tenantId,
						isActive: true,
						deletedAt: null
					},
					orderBy: [
						{ paymentType: 'asc' },
						{ name: 'asc' },
					]
				}),
				ctx.db.deductionComponent.findMany({
					where: {
						tenantId: ctx.tenantId,
						isActive: true,
						deletedAt: null
					},
					orderBy: [
						{ deductionType: 'asc' },
						{ name: 'asc' },
					]
				})
			]);

			return {
				paymentComponents,
				deductionComponents
			};
		}),
});

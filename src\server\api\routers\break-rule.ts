import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";

// Input validation schemas
const createBreakRuleSchema = z.object({
  agreementId: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  minWorkHours: z.number().min(0).max(24),
  breakDuration: z.number().min(1).max(240), // max 4 hours
  isPaid: z.boolean().default(false),
  isMandatory: z.boolean().default(true),
  canBeSplit: z.boolean().default(false),
  minSplitDuration: z.number().min(1).max(60).optional(),
});

const updateBreakRuleSchema = createBreakRuleSchema.extend({
  id: z.string(),
});

export const breakRuleRouter = createTRPCRouter({
  // Get all break rules
  getAll: publicProcedure
    .input(
      z.object({
        agreementId: z.string().optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where = input?.agreementId 
        ? { agreementId: input.agreementId, deletedAt: null }
        : { deletedAt: null };
      
      return ctx.db.breakRule.findMany({
        where,
        orderBy: {
          minWorkHours: "asc",
        },
      });
    }),

  // Get break rule by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const rule = await ctx.db.breakRule.findUnique({
        where: { id: input.id },
      });

      if (!rule || rule.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "חוק הפסקות לא נמצא",
        });
      }

      return rule;
    }),
  // Create new break rule
  create: protectedProcedure
    .input(createBreakRuleSchema)
    .mutation(async ({ ctx, input }) => {
      // Validate split duration
      if (input.canBeSplit && !input.minSplitDuration) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "יש להגדיר משך מינימלי לפיצול כאשר ניתן לפצל הפסקות",
        });
      }

      if (input.minSplitDuration && input.minSplitDuration > input.breakDuration) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "משך פיצול מינימלי לא יכול להיות גדול ממשך ההפסקה",
        });
      }

      const { tenantId } = ctx.session.user;

      return ctx.db.breakRule.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update break rule
  update: protectedProcedure
    .input(updateBreakRuleSchema)
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const { id, ...data } = input;

      // Check if rule exists
      const existing = await ctx.db.breakRule.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "חוק הפסקות לא נמצא",
        });
      }

      // Validate split duration
      if (data.canBeSplit && !data.minSplitDuration) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "יש להגדיר משך מינימלי לפיצול כאשר ניתן לפצל הפסקות",
        });
      }

      if (data.minSplitDuration && data.minSplitDuration > data.breakDuration) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "משך פיצול מינימלי לא יכול להיות גדול ממשך ההפסקה",
        });
      }

      return ctx.db.breakRule.update({
        where: { id },
        data,
      });
    }),

  // Delete break rule (soft delete)
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      // Check if rule exists
      const existing = await ctx.db.breakRule.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "חוק הפסקות לא נמצא",
        });
      }

      return ctx.db.breakRule.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
}); 
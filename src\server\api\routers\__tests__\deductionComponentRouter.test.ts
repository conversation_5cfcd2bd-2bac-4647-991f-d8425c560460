import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { deductionComponentRouter } = await import('../deduction-component');
const createCaller = createCallerFactory(deductionComponentRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('deductionComponentRouter.getUsageStats', () => {
  it('aggregates payslip item stats', async () => {
    const { Prisma } = await import('@prisma/client');
    const dbMock = {
      deductionComponent: {
        findMany: vi.fn().mockResolvedValue([
          { id: 'd1', code: 'd', name: 'D', tenantId: 't1', _count: { payslipItems: 2 } },
        ]),
      },
      payslipItem: {
        findMany: vi.fn().mockResolvedValue([
          { deductionComponentId: 'd1', amount: new Prisma.Decimal(5), payslip: { employeeId: 'e1', year: 2024, month: 4 } },
          { deductionComponentId: 'd1', amount: new Prisma.Decimal(7), payslip: { employeeId: 'e2', year: 2024, month: 6 } },
        ]),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock });
    const res = await caller.getUsageStats();
    expect(res[0].activeEmployees).toBe(2);
    expect(res[0].totalAmount).toBe(12);
    expect(res[0].lastUsed).toEqual(new Date(2024, 5, 1));
  });
});

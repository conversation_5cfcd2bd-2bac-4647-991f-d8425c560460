import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { settingRouter } = await import('../setting');
const createCaller = createCallerFactory(settingRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('settingRouter.getByKey', () => {
  it('returns setting value', async () => {
    const dbMock = {
      user: { findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }) },
      setting: {
        findUnique: vi.fn().mockResolvedValue({ key: 'TAX_BRACKETS_2025', value: [1] }),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock, session: { user: { id: 'u1' } } });
    const res = await caller.getByKey({ key: 'TAX_BRACKETS_2025' });
    expect(res.value).toEqual([1]);
  });
});

"use client";

import { useEffect, useState } from "react";
import { useMasterDetail } from "../../layout";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Calendar, Mail, Phone, Edit, Download, FileText } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useEmployee } from "@/hooks/employee-hooks";
import { Skeleton } from "@/components/ui/skeleton";
import { EmployeeForm } from "./employee-form";

export function EmployeeDetails() {
  const { selectedItemId } = useMasterDetail();
  const [activeTab, setActiveTab] = useState("details");
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  
  // Fetch employee data from API
  const { data: employee, isLoading, error } = useEmployee(selectedItemId);

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6 animate-in fade-in duration-300">
        <div className="flex flex-col items-center text-center">
          <Skeleton className="h-24 w-24 rounded-full mb-4" />
          <Skeleton className="h-6 w-40 mb-2" />
          <Skeleton className="h-4 w-32 mb-4" />
        </div>
        
        <div className="flex justify-center gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-24" />
        </div>
        
        <Separator />
        
        <div>
          <Skeleton className="h-10 w-full mb-6" />
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !employee) {
    return (
      <div className="flex h-[calc(100vh-16rem)] flex-col items-center justify-center text-center space-y-4">
        <div className="rounded-full bg-red-100 p-3">
          <FileText className="h-6 w-6 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold">שגיאה בטעינת נתונים</h3>
        <p className="text-sm text-muted-foreground">לא ניתן לטעון את פרטי העובד. אנא נסה שוב.</p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="mt-2"
        >
          רענן
        </Button>
      </div>
    );
  }

  const handleEdit = () => {
    setIsEditFormOpen(true);
  };

  return (
    <>
      <div className="space-y-6 animate-in fade-in duration-300">
        {/* Employee Header */}
        <div className="flex flex-col items-center text-center">
          <Avatar className="h-24 w-24 mb-4">
            <AvatarImage src="" alt={employee.name} />
            <AvatarFallback className="text-xl bg-primary/10 text-primary">
              {employee.name.split(" ").map(n => n[0]).join("")}
            </AvatarFallback>
          </Avatar>
          <h2 className="text-xl font-bold">{employee.name}</h2>
          <p className="text-muted-foreground">{employee.position}</p>
          <div className="mt-2">
            <Badge
              variant={employee.status === "פעיל" ? "default" : "outline"}
              className={
                employee.status === "פעיל"
                  ? "bg-green-100 text-green-800 hover:bg-green-100/80"
                  : "border-yellow-500 text-yellow-700"
              }
            >
              {employee.status}
            </Badge>
          </div>
        </div>

        <div className="flex justify-center gap-2">
          <Button variant="outline" size="sm" className="flex gap-1" onClick={handleEdit}>
            <Edit className="h-4 w-4" />
            <span>עריכה</span>
          </Button>
          <Button variant="outline" size="sm" className="flex gap-1">
            <Download className="h-4 w-4" />
            <span>ייצוא פרטים</span>
          </Button>
        </div>

        <Separator />

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="details">פרטים</TabsTrigger>
            <TabsTrigger value="salary">שכר</TabsTrigger>
            <TabsTrigger value="documents">מסמכים</TabsTrigger>
          </TabsList>
          
          <TabsContent value="details" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">מחלקה</p>
                <p className="font-medium">{employee.department}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">מיקום</p>
                <p className="font-medium">{employee.location || "לא צוין"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">מנהל</p>
                <p className="font-medium">{employee.manager || "לא צוין"}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">תאריך תחילת עבודה</p>
                <p className="font-medium flex items-center">
                  <Calendar className="ml-1 h-4 w-4 text-muted-foreground" />
                  {employee.startDate}
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">דוא״ל</p>
                <p className="font-medium flex items-center">
                  <Mail className="ml-1 h-4 w-4 text-muted-foreground" />
                  {employee.email}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">טלפון</p>
                <p className="font-medium flex items-center">
                  <Phone className="ml-1 h-4 w-4 text-muted-foreground" />
                  {employee.phone || "לא צוין"}
                </p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="salary" className="space-y-4 py-4">
            <div className="rounded-md border p-4">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">שכר נוכחי</p>
                <p className="text-xl font-bold">{employee.salary}</p>
                <p className="text-xs text-muted-foreground">עודכן לאחרונה: 01/04/2023</p>
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>שכר בסיס</span>
                  <span>₪ 10,000</span>
                </div>
                <div className="flex justify-between">
                  <span>תוספות</span>
                  <span>₪ 2,500</span>
                </div>
                <div className="flex justify-between">
                  <span>בונוסים</span>
                  <span>₪ 0</span>
                </div>
              </div>
            </div>
            
            <Button className="w-full">
              הצג היסטוריית שכר
            </Button>
          </TabsContent>
          
          <TabsContent value="documents" className="space-y-4 py-4">
            <div className="rounded-md border divide-y">
              <div className="flex items-center justify-between p-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span>חוזה העסקה</span>
                </div>
                <Button variant="ghost" size="sm">
                  צפייה
                </Button>
              </div>
              <div className="flex items-center justify-between p-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span>טופס 101</span>
                </div>
                <Button variant="ghost" size="sm">
                  צפייה
                </Button>
              </div>
              <div className="flex items-center justify-between p-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <span>תלוש שכר אחרון</span>
                </div>
                <Button variant="ghost" size="sm">
                  צפייה
                </Button>
              </div>
            </div>
            
            <Button variant="outline" className="w-full">
              העלאת מסמך חדש
            </Button>
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Employee Form */}
      <EmployeeForm
        isOpen={isEditFormOpen}
        onClose={() => setIsEditFormOpen(false)}
        initialData={employee}
        isEdit={true}
      />
    </>
  );
} 
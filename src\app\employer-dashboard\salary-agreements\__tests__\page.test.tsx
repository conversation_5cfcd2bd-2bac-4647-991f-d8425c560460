// @vitest-environment node
import React from "react";
import { fireEvent, render } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import SalaryAgreementsPage from "../page";

vi.mock("../hooks", () => ({
	useSalaryAgreements: vi.fn(() => ({ data: [], isLoading: false })),
	useDeleteSalaryAgreement: vi.fn(() => ({ mutateAsync: vi.fn() })),
}));

vi.mock("../components/SalaryAgreementFormModal", () => ({
	__esModule: true,
	default: (props: { open: boolean }) =>
		props.open ? <div data-testid="modal" /> : null,
}));

vi.mock("../components/SalaryAgreementsTable", () => ({
	__esModule: true,
	default: () => <div data-testid="table" />,
}));

describe.skip("SalaryAgreementsPage", () => {
	it("opens modal when clicking add button", () => {
		const { getByText, queryByTestId } = render(<SalaryAgreementsPage />);
		expect(queryByTestId("modal")).toBeNull();
		fireEvent.click(getByText("הוספת הסכם"));
		expect(queryByTestId("modal")).toBeInTheDocument();
	});
});

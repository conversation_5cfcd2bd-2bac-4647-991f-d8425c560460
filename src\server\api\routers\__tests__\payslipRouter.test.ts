import { describe, expect, it, vi } from "vitest";
import { Prisma } from "@prisma/client";
process.env.SKIP_ENV_VALIDATION = "true";
vi.mock("@/server/auth", () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import("../../trpc");
const { payslipRouter } = await import("../payslip");
const createCaller = createCallerFactory(payslipRouter);

const baseCtx = {
	db: {},
	logger: undefined,
	headers: new Headers(),
} as unknown as Record<string, unknown>;

describe("payslipRouter.calculate", () => {
	it("throws when employee not found", async () => {
		const dbMock = {
			employee: { findUnique: vi.fn().mockResolvedValue(null) },
		} as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1", tenantId: "t1" } },
                });
		await expect(
			caller.calculate({ employeeId: "e1", month: 5, year: 2024 }),
		).rejects.toBeInstanceOf(Error);
	});

        it("creates payslip with transactions", async () => {

                const dbMock = {
                        employee: {
                                findUnique: vi
                                        .fn()
                                        .mockResolvedValue({
                                                id: "e1",
                                                tenantId: "t1",
                                                baseSalary: 1000,
                                                salaryRecords: [{ basis: 'MONTHLY' }],
                                        }),
                        },
			salaryTransaction: {
				findMany: vi.fn().mockResolvedValue([
					{
						id: "t1",

						amount: 100,
						description: "ot",
						rate: null,
						quantity: null,
						percentage: null,
					},
					{
						id: "t2",
						amount: -50,
						description: "ded",
						rate: null,
						quantity: null,
						percentage: null,
					},
				]),
				updateMany: vi.fn().mockResolvedValue({}),
			},
                        payslip: {
                                findFirst: vi.fn().mockResolvedValue(null),
                                create: vi.fn().mockResolvedValue({ id: "p1" }),
                        },
                        salaryTemplateComponent: { findMany: vi.fn().mockResolvedValue([]) },
                        paymentComponent: { findMany: vi.fn().mockResolvedValue([]) },
                        formula: { findMany: vi.fn().mockResolvedValue([]) },
                        deductionComponent: { findMany: vi.fn().mockResolvedValue([]) },
                        valueComponent: { findMany: vi.fn().mockResolvedValue([]) },
                        form101: { findFirst: vi.fn().mockResolvedValue(null) },
                        $transaction: vi.fn(async (fn) => fn(dbMock)),
                } as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1", tenantId: "t1" } },
                });
		const res = await caller.calculate({
			employeeId: "e1",
			month: 5,
			year: 2024,
		});
		expect(dbMock.payslip.create).toHaveBeenCalled();
                expect(res).toEqual({ id: "p1" });
        });
});

describe("payslipRouter.getById", () => {
        it("rejects when user has no tenant", async () => {
                const dbMock = {
                        user: { findUnique: vi.fn().mockResolvedValue(null) },
                } as unknown as Record<string, unknown>;

                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1" } },
                });
                await expect(caller.getById("p1")).rejects.toBeInstanceOf(Error);
        });
});

describe("payslipRouter.update", () => {
        it("rejects editing non-draft payslip", async () => {
                const dbMock = {
                        user: { findUnique: vi.fn().mockResolvedValue({ tenantId: "t1" }) },
                        payslip: { findUnique: vi.fn().mockResolvedValue({ status: "PAID" }) },
                } as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1" } },
                });
                await expect(
                        caller.update({ id: "p1", grossPay: 100 })
                ).rejects.toBeInstanceOf(Error);
        });
});

describe("payslipRouter.delete", () => {
        it("rejects deleting paid payslip", async () => {
                const dbMock = {
                        user: { findUnique: vi.fn().mockResolvedValue({ tenantId: "t1" }) },
                        payslip: { findUnique: vi.fn().mockResolvedValue({ status: "PAID" }) },
                } as unknown as Record<string, unknown>;
                const caller = createCaller({
                        ...baseCtx,
                        db: dbMock,
                        session: { user: { id: "u1" } },
                });
                await expect(caller.delete("p1")).rejects.toBeInstanceOf(Error);
        });
});

describe("payslipRouter.calculate input validation", () => {
        it("rejects invalid month", async () => {
                const caller = createCaller({
                        ...baseCtx,
                        db: {},
                        session: { user: { id: "u1" } },
                });
                await expect(
                        // @ts-expect-error invalid month
                        caller.calculate({ employeeId: "e1", month: 13, year: 2024 })
                ).rejects.toBeInstanceOf(Error);

        });
});

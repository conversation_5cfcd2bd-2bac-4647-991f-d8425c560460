# Contributing

Thank you for considering a contribution!

This repository follows the guidelines defined in [`.cursorrules`](./.cursorrules). Please read that document before opening a pull request.

## Commit Messages

All commit messages **must** be written in Hebrew as specified in the Git conventions section of `.cursorrules`.

## <PERSON>sky Hook

A Husky `commit-msg` hook is included to verify commit messages contain Hebrew characters. After installing dependencies run `pnpm prepare` (or reinstall packages) to ensure Husky hooks are set up.


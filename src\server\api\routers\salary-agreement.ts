import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import type { Prisma } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";
import { z } from "zod";

const baseSchema = z.object({
  employerId: z.string(),
  name: z.string().min(1),
  description: z.string().optional(),
  effectiveFrom: z.date().optional(),
  effectiveTo: z.date().optional(),
  terms: z.any().optional(),
  isActive: z.boolean().default(true),
});

const createSchema = baseSchema;
const updateSchema = baseSchema.extend({ id: z.string() });

export const salaryAgreementRouter = createTRPCRouter({
  getAll: publicProcedure
    .input(z.object({ employerId: z.string().optional() }).optional())
    .query(async ({ ctx, input }) => {
      const where: Prisma.SalaryAgreementWhereInput = {};
      if (input?.employerId) {
        where.employerId = input.employerId;
      }
      return ctx.db.salaryAgreement.findMany({
        where,
        orderBy: { createdAt: "desc" },
      });
    }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const agreement = await ctx.db.salaryAgreement.findUnique({
        where: { id: input.id },
      });
      if (!agreement) {
        throw new TRPCError({ code: "NOT_FOUND", message: "הסכם לא נמצא" });
      }
      return agreement;
    }),
  create: protectedProcedure
    .input(createSchema)
    .mutation(async ({ ctx, input }) => {
      const existing = await ctx.db.salaryAgreement.findFirst({
        where: {
          employerId: input.employerId,
          name: input.name,
        },
      });
      if (existing) {
        throw new TRPCError({ code: "CONFLICT", message: "שם הסכם כבר קיים" });
      }
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      return ctx.db.salaryAgreement.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  update: protectedProcedure
    .input(updateSchema)
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const { id, ...data } = input;
      const agreement = await ctx.db.salaryAgreement.findUnique({
        where: { id },
      });
      if (!agreement) {
        throw new TRPCError({ code: "NOT_FOUND", message: "הסכם לא נמצא" });
      }
      if (data.name !== agreement.name) {
        const nameExists = await ctx.db.salaryAgreement.findFirst({
          where: {
            employerId: agreement.employerId,
            name: data.name,
            id: { not: id },
          },
        });
        if (nameExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "שם הסכם כבר קיים",
          });
        }
      }
      return ctx.db.salaryAgreement.update({ where: { id }, data });
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const agreement = await ctx.db.salaryAgreement.findUnique({
        where: { id: input.id },
      });
      if (!agreement) {
        throw new TRPCError({ code: "NOT_FOUND", message: "הסכם לא נמצא" });
      }
      return ctx.db.salaryAgreement.delete({
        where: { id: input.id },
      });
    }),
});

import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
  tenantProcedure,
  createCallerFactory,
} = await import('@/server/api/trpc');

const router = createTRPCRouter({
  open: publicProcedure.query(() => 'ok'),
  secure: protectedProcedure.query(({ ctx }) => ctx.session.user.id),
});

const createCaller = createCallerFactory(router);
const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('protectedProcedure', () => {
  it('rejects unauthenticated calls', async () => {
    const caller = createCaller({ ...baseCtx, session: null });
    await expect(caller.secure()).rejects.toMatchObject({ code: 'UNAUTHORIZED' });
  });

  it('allows authenticated calls', async () => {
    const caller = createCaller({ ...baseCtx, session: { user: { id: 'u1', tenantId: 't1' } } });
    const res = await caller.secure();
    expect(res).toBe('u1');
  });
});

describe('tenantProcedure', () => {
  const tenantRouter = createTRPCRouter({
    tenant: tenantProcedure.query(({ ctx }) => ctx.tenantId),
  });
  const createTenantCaller = createCallerFactory(tenantRouter);

  it('rejects when tenantId missing', async () => {
    const caller = createTenantCaller({ ...baseCtx, session: { user: { id: 'u1' } } });
    await expect(caller.tenant()).rejects.toMatchObject({ code: 'FORBIDDEN' });
  });

  it('returns tenantId from context', async () => {
    const caller = createTenantCaller({
      ...baseCtx,
      session: { user: { id: 'u1' } },
      tenantId: 't1',
    });
    const res = await caller.tenant();
    expect(res).toBe('t1');
  });
});

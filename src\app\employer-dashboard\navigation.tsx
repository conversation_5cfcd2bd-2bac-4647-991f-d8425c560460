import {
	Building,
	Calculator,
	Clock,
	FileText,
	Home,
	Link2,
	Receipt,
	Settings,
	Users,
	LayoutDashboard,
	CreditCard,
	Calendar,
	BarChart3
} from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";
import type { ReactNode } from "react";

const container = {
	hidden: { opacity: 0 },
	show: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
		},
	},
};

const item = {
	hidden: { opacity: 0, x: 20 }, // Changed from -20 to 20 for RTL
	show: {
		opacity: 1,
		x: 0,
		transition: {
			type: "spring",
			stiffness: 100,
			damping: 10,
		},
	},
};

interface NavItem {
	title: string;
	href: string;
	icon: ReactNode;
}

export const employerDashboardNavItems: NavItem[] = [
	{
		title: "דשבורד",
		href: "/employer-dashboard",
		icon: <LayoutDashboard className="h-5 w-5" />
	},
	{
		title: "עובדים",
		href: "/employer-dashboard/employees",
		icon: <Users className="h-5 w-5" />
	},
	{
		title: "מחלקות",
		href: "/employer-dashboard/departments",
		icon: <Building className="h-5 w-5" />
	},
	{
		title: "שכר",
		href: "/employer-dashboard/salary",
		icon: <CreditCard className="h-5 w-5" />
	},
	{
		title: "תלושים",
		href: "/employer-dashboard/payslips",
		icon: <FileText className="h-5 w-5" />
	},
	{
		title: "נוכחות",
		href: "/employer-dashboard/attendance",
		icon: <Calendar className="h-5 w-5" />
	},
	{
		title: "דוחות",
		href: "/employer-dashboard/reports",
		icon: <BarChart3 className="h-5 w-5" />
	},
	{
		title: "חברה",
		href: "/employer-dashboard/company",
		icon: <Home className="h-5 w-5" />
	},
	{
		title: "הגדרות",
		href: "/employer-dashboard/settings",
		icon: <Settings className="h-5 w-5" />
	}
];

export const AnimatedNavigation = () => {
	return (
		<motion.nav
			variants={container}
			initial="hidden"
			animate="show"
			dir="rtl"
			className="space-y-2"
		>
			{employerDashboardNavItems.map((navItem) => (
				<motion.div key={navItem.href} variants={item}>
					<Link
						href={navItem.href}
						className="flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900 hover:bg-gray-100"
					>
						{navItem.icon}
						<span className="font-normal">{navItem.title}</span>
					</Link>
				</motion.div>
			))}
		</motion.nav>
	);
};

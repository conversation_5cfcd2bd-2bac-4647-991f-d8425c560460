"use client";

import { useState, useEffect } from "react";
import { useMasterDetail } from "../layout";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Search, Building, Users } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DepartmentDetails } from "./components/department-details";
import { DepartmentForm } from "./components/department-form";
import { useDepartmentObjects } from "@/hooks/employee-hooks";
import { Skeleton } from "@/components/ui/skeleton";

export default function DepartmentsPage() {
  const { selectedItemId, setSelectedItemId, setIsDetailOpen, setDetailTitle } = useMasterDetail();
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [isDepartmentFormOpen, setIsDepartmentFormOpen] = useState(false);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch departments from API
  const { data: departmentsData, isLoading, error } = useDepartmentObjects(debouncedSearch);

  const departments = departmentsData?.items || [];

  // When department is selected, update the detail panel title
  useEffect(() => {
    if (selectedItemId) {
      const department = departments.find(dept => dept.id === selectedItemId);
      if (department) {
        setDetailTitle(`פרטי מחלקה: ${department.name}`);
      }
    }
  }, [selectedItemId, departments, setDetailTitle]);

  // Handle department selection
  const handleSelectDepartment = (departmentId: string) => {
    setSelectedItemId(departmentId);
    setIsDetailOpen(true);
  };

  // Open form to add new department
  const handleAddDepartment = () => {
    setIsDepartmentFormOpen(true);
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-2xl font-bold">מחלקות</h1>
            <p className="text-muted-foreground">ניהול מחלקות ומבנה ארגוני</p>
          </div>
          <Button onClick={handleAddDepartment} className="self-start md:self-auto">
            <PlusCircle className="ml-2 h-4 w-4" />
            מחלקה חדשה
          </Button>
        </div>

        <div className="relative">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="חיפוש מחלקות..."
            className="pr-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {isLoading ? (
          // Loading skeleton
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={`loading-${index}`} className="h-48">
                <CardHeader className="pb-2">
                  <Skeleton className="h-5 w-24 mb-1" />
                  <Skeleton className="h-4 w-32" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </CardContent>
                <CardFooter>
                  <Skeleton className="h-8 w-20" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="rounded-md border p-8 text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Building className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="mt-4 text-lg font-medium">שגיאה בטעינת נתונים</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              לא ניתן לטעון את המחלקות. אנא נסה שוב מאוחר יותר.
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              רענן
            </Button>
          </div>
        ) : departments.length === 0 ? (
          // Empty state
          <div className="rounded-md border p-8 text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
              <Building className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="mt-4 text-lg font-medium">
              {debouncedSearch ? "לא נמצאו מחלקות מתאימות" : "אין מחלקות להצגה"}
            </h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {debouncedSearch ? 
                "נסה חיפוש אחר" : 
                "לחץ על כפתור 'מחלקה חדשה' ליצירת מחלקה"}
            </p>
            {debouncedSearch && (
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setSearchQuery("")}
              >
                נקה חיפוש
              </Button>
            )}
          </div>
        ) : (
          // Departments grid
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {departments.map((department) => (
              <Card
                key={department.id}
                className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                  selectedItemId === department.id ? "bg-muted" : ""
                }`}
                onClick={() => handleSelectDepartment(department.id)}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">{department.name}</CardTitle>
                  <CardDescription>{department.manager || "אין מנהל"}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {department.description || "אין תיאור"}
                  </p>
                </CardContent>
                <CardFooter>
                  <div className="flex items-center text-muted-foreground text-sm">
                    <Users className="h-4 w-4 mr-1" />
                    <span>{department.employeeCount} עובדים</span>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Hidden component to render in the detail panel */}
      {selectedItemId && <DepartmentDetails />}
      
      {/* Department form dialog */}
      <DepartmentForm
        isOpen={isDepartmentFormOpen}
        onClose={() => setIsDepartmentFormOpen(false)}
      />
    </>
  );
} 
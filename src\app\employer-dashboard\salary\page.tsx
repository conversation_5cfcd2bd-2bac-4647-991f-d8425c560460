"use client";

import {
	Button,
	Input,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/rtl-components";
import { useMemo, useState } from "react";

const TAX_RATES: Record<string, number> = {
	"2025-1": 0.1,
	"2025-2": 0.1,
	"2025-3": 0.12,
	"2025-4": 0.12,
	"2025-5": 0.13,
	// default fallback rates
};

const months = [
	"ינואר",
	"פברואר",
	"מרץ",
	"אפריל",
	"מאי",
	"יוני",
	"יולי",
	"אוגוסט",
	"ספטמבר",
	"אוקטובר",
	"נובמבר",
	"דצמבר",
];

export default function SalaryPage() {
	const [month, setMonth] = useState("1");
	const [year, setYear] = useState("2025");
	const [gross, setGross] = useState(10000);

	const rate = useMemo(
		() => TAX_RATES[`${year}-${month}`] ?? 0.15,
		[year, month],
	);
	const net = useMemo(() => gross - gross * rate, [gross, rate]);

	return (
		<div className="container mx-auto space-y-6 py-6" dir="rtl">
			<div>
				<h1 className="font-bold text-2xl">חישוב שכר</h1>
				<p className="text-muted-foreground">חשב נטו לפי חודש ושנה</p>
			</div>
			<div className="flex flex-wrap items-end gap-4">
				<div className="space-y-2">
					<label htmlFor="year" className="block font-medium text-sm">
						שנה
					</label>
					<Input
						id="year"
						value={year}
						onChange={(e) => setYear(e.target.value)}
						type="number"
					/>
				</div>
				<div className="space-y-2">
					<label htmlFor="month" className="block font-medium text-sm">
						חודש
					</label>
					<Select value={month} onValueChange={setMonth}>
						<SelectTrigger className="w-32">
							<SelectValue placeholder="חודש" />
						</SelectTrigger>
						<SelectContent>
							{months.map((m, index) => {
								const value = (index + 1).toString();
								return (
									<SelectItem key={value} value={value}>
										{m}
									</SelectItem>
								);
							})}
						</SelectContent>
					</Select>
				</div>
				<div className="space-y-2">
					<label htmlFor="gross" className="block font-medium text-sm">
						ברוטו
					</label>
					<Input
						id="gross"
						type="number"
						value={gross}
						onChange={(e) => setGross(Number(e.target.value))}
					/>
				</div>
			</div>
			<div className="space-y-2">
				<p>מס לחודש: {(rate * 100).toFixed(1)}%</p>
				<p>נטו: {net.toFixed(2)} ₪</p>
			</div>
			<Button onClick={() => alert(`הנטו לחודש הנבחר הוא ${net.toFixed(2)} ₪`)}>
				חשב
			</Button>
		</div>
	);
}

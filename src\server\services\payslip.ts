import {
	calculateForeignWorkerDeductions,
	calculateIncomeTax,
	calculateNationalInsurance,
	calculateOvertimePay,
	validatePayslipConsistency,
} from "@/utils/payroll-calculations";
import {
	AssociationType,
	DeductionType,
	FormulaType,
	PaymentType,
	PayslipItemKod,
	PayslipItemType,
	Prisma,
	type PrismaClient,
} from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { Parser } from "expr-eval";

export interface CalculatePayslipInput {
	employeeId: string;
	month: number;
	year: number;
}

export interface WorkHoursInput {
	regularHours?: number;
	overtime125?: number;
	overtime150?: number;
	overtime175?: number;
	overtime200?: number;
}

export function sumContributionTotals(
	items: {
		kod?: PayslipItemKod | null;
		amount: Prisma.Decimal | number | string | Prisma.DecimalJsLike;
	}[],
) {
	const toNum = (a: Prisma.Decimal | number | string | Prisma.DecimalJsLike) =>
		Number(a);
	const pensionEmployee = items
		.filter((i) => i.kod === PayslipItemKod.K_PENSION_EMP)
		.reduce((s, i) => s + toNum(i.amount), 0);
	const pensionEmployer = items
		.filter((i) => i.kod === PayslipItemKod.K_0313)
		.reduce((s, i) => s + toNum(i.amount), 0);
	const severancePay = items
		.filter((i) => i.kod === PayslipItemKod.K_0250)
		.reduce((s, i) => s + toNum(i.amount), 0);
	return { pensionEmployee, pensionEmployer, severancePay };
}

export async function calculatePayslip(
	db: PrismaClient,
	{ employeeId, month, year }: CalculatePayslipInput,
	workHours?: WorkHoursInput,
) {
	// Load employee with all relevant data including associations, agreements, and components
	const employee = await db.employee.findUnique({
		where: { id: employeeId },
		include: {
			department: true,
			employeeAgreements: {
				include: {
					agreement: {
						include: {
							shifts: true,
							overtimeRules: true,
							breakRules: true,
						},
					},
				},
				where: {
					OR: [
						{ endDate: null },
						{ endDate: { gte: new Date(year, month - 1, 1) } },
					],
					startDate: { lte: new Date(year, month, 0) },
				},
			},
			salaryRecords: {
				where: {
					effectiveFrom: { lte: new Date(year, month - 1, 1) },
					OR: [
						{ effectiveTo: null },
						{ effectiveTo: { gte: new Date(year, month - 1, 1) } },
					],
				},
				orderBy: { effectiveFrom: "desc" },
				take: 1,
			},
			associations: {
				include: {
					department: true,
					role: true,
				},
				where: {
					OR: [
						{ endDate: null },
						{ endDate: { gte: new Date(year, month - 1, 1) } },
					],
					startDate: { lte: new Date(year, month, 0) },
				},
			},
		},
	});

	if (!employee) {
		throw new TRPCError({ code: "NOT_FOUND", message: "Employee not found" });
	}

	// Get active salary record
	const salaryRecord = employee.salaryRecords[0];
	if (!salaryRecord) {
		throw new TRPCError({
			code: "NOT_FOUND",
			message: "No salary record found for employee",
		});
	}

	// Load salary transactions for period
	const transactions = await db.salaryTransaction.findMany({
		where: {
			employeeId,
			periodMonth: month,
			periodYear: year,
		},
	});

	// Load applicable payment components based on associations
	const salaryTemplateIds = employee.associations
		.filter(
			(a) =>
				a.associationType === AssociationType.SALARY_TEMPLATE &&
				a.salaryTemplateId,
		)
		.map((a) => a.salaryTemplateId as string)
		.filter(Boolean);

	// Get payment components from salary templates
	const templateComponents =
		salaryTemplateIds.length > 0
			? await db.salaryTemplateComponent.findMany({
					where: {
						templateId: { in: salaryTemplateIds },
						isActive: true,
						OR: [
							{ effectiveFrom: null },
							{ effectiveFrom: { lte: new Date(year, month - 1, 1) } },
						],
					},
				})
			: [];

	// Get payment components
	const paymentComponents = await db.paymentComponent.findMany({
		where: {
			tenantId: employee.tenantId,
			isActive: true,
			OR: [{ employerId: null }, { employerId: employee.employerId }],
		},
	});

	// Get formulas
	const formulas = await db.formula.findMany({
		where: {
			tenantId: employee.tenantId,
			status: "ACTIVE",
			employerId: {
				in: [employee.employerId],
			},
			startDate: { lte: new Date(year, month - 1, 1) },
			OR: [
				{ endDate: null },
				{ endDate: { gte: new Date(year, month - 1, 1) } },
			],
		},
	});

	// Get deduction components
	const deductionComponents = await db.deductionComponent.findMany({
		where: {
			tenantId: employee.tenantId,
			isActive: true,
			OR: [{ employerId: null }, { employerId: employee.employerId }],
		},
	});

	// Get value components for calculations
	const valueComponents = await db.valueComponent.findMany({
		where: {
			tenantId: employee.tenantId,
			isActive: true,
		},
	});

	// Create a map of value components for easy access
	const valueMap = new Map(valueComponents.map((vc) => [vc.code, vc]));

	const items: Prisma.PayslipItemCreateWithoutPayslipInput[] = [];
	let totalEarnings = 0;
	let totalDeductions = 0;

	// Calculate base salary and overtime
	if (salaryRecord.basis === "HOURLY" && salaryRecord.hourlyRate) {
		// Calculate hourly-based salary with overtime
		const hourlyRate = Number(salaryRecord.hourlyRate);
		const regularHours =
			workHours?.regularHours ||
			Number(salaryRecord.standardMonthlyHours || 186);
		const overtime125 = workHours?.overtime125 || 0;
		const overtime150 = workHours?.overtime150 || 0;
		const overtime175 = workHours?.overtime175 || 0;
		const overtime200 = workHours?.overtime200 || 0;

		const overtimeCalc = calculateOvertimePay(
			regularHours,
			overtime125,
			overtime150,
			overtime175,
			overtime200,
			hourlyRate,
		);

		if (!overtimeCalc.isValid) {
			throw new TRPCError({
				code: "BAD_REQUEST",
				message: `שגיאה בחישוב שעות: ${overtimeCalc.errors.join(", ")}`,
			});
		}

		// Add regular pay
		if (overtimeCalc.regularPay > 0) {
			items.push({
				description: "שכר יסוד",
				amount: new Prisma.Decimal(overtimeCalc.regularPay),
				type: PayslipItemType.EARNING,
				kod: PayslipItemKod.K_1000,
				units: new Prisma.Decimal(regularHours),
				rate: new Prisma.Decimal(hourlyRate),
			});
			totalEarnings += overtimeCalc.regularPay;
		}

		// Add overtime items based on attendance agreement rules
		const attendanceAgreement = employee.employeeAgreements[0]?.agreement;
		if (attendanceAgreement?.overtimeRules) {
			// Apply overtime rules from agreement
			if (overtime125 > 0) {
				items.push({
					description: "שעות נוספות 125%",
					amount: new Prisma.Decimal(overtimeCalc.overtime125Pay),
					type: PayslipItemType.EARNING,
					kod: PayslipItemKod.K_1020,
					units: new Prisma.Decimal(overtime125),
					rate: new Prisma.Decimal(hourlyRate * 1.25),
				});
				totalEarnings += overtimeCalc.overtime125Pay;
			}

			if (overtime150 > 0) {
				items.push({
					description: "שעות נוספות 150%",
					amount: new Prisma.Decimal(overtimeCalc.overtime150Pay),
					type: PayslipItemType.EARNING,
					kod: PayslipItemKod.K_1021,
					units: new Prisma.Decimal(overtime150),
					rate: new Prisma.Decimal(hourlyRate * 1.5),
				});
				totalEarnings += overtimeCalc.overtime150Pay;
			}

			if (overtime175 > 0) {
				items.push({
					description: "שעות נוספות 175%",
					amount: new Prisma.Decimal(overtimeCalc.overtime175Pay),
					type: PayslipItemType.EARNING,
					kod: PayslipItemKod.K_1022,
					units: new Prisma.Decimal(overtime175),
					rate: new Prisma.Decimal(hourlyRate * 1.75),
				});
				totalEarnings += overtimeCalc.overtime175Pay;
			}

			if (overtime200 > 0) {
				items.push({
					description: "שעות נוספות 200%",
					amount: new Prisma.Decimal(overtimeCalc.overtime200Pay),
					type: PayslipItemType.EARNING,
					kod: PayslipItemKod.K_1022,
					units: new Prisma.Decimal(overtime200),
					rate: new Prisma.Decimal(hourlyRate * 2.0),
				});
				totalEarnings += overtimeCalc.overtime200Pay;
			}
		}
	} else {
		// Monthly salary
		const baseSalary = Number(salaryRecord.amount);
		if (baseSalary > 0) {
			items.push({
				description: "משכורת יסוד",
				amount: new Prisma.Decimal(baseSalary),
				type: PayslipItemType.EARNING,
				kod: PayslipItemKod.K_1000,
			});
			totalEarnings += baseSalary;
		}
	}

	// Process payment components from templates
	for (const templateComp of templateComponents) {
		// Find matching payment component
		const paymentComp = paymentComponents.find(
			(pc) => pc.code === templateComp.componentCode,
		);
		if (!paymentComp) continue;

		let componentAmount = 0;

		// Check calculation type
		if (templateComp.calculationType === "fixed" && templateComp.fixedAmount) {
			componentAmount = Number(templateComp.fixedAmount);
		} else if (
			templateComp.calculationType === "percentage" &&
			templateComp.percentage
		) {
			// Calculate percentage of base salary
			componentAmount =
				(Number(salaryRecord.amount) * Number(templateComp.percentage)) / 100;
		} else if (
			templateComp.calculationType === "formula" &&
			templateComp.formula
		) {
			// Find matching formula
			const formula = formulas.find(
				(f) => f.formulaCode === templateComp.formula,
			);
			if (formula) {
				componentAmount = await calculateFormulaValue(formula, {
					baseSalary: Number(salaryRecord.amount),
					hourlyRate: Number(salaryRecord.hourlyRate || 0),
					workDays: Number(salaryRecord.workedDays || 22),
					workHours: Number(
						salaryRecord.workedHours || workHours?.regularHours || 186,
					),
					valueMap,
					employee,
					db,
					month,
					year,
				});
			}
		}

		if (componentAmount > 0) {
			// Determine item type based on payment type
			let itemType: PayslipItemType = PayslipItemType.EARNING;
			if (
				paymentComp.paymentType === PaymentType.EXPENSE ||
				paymentComp.paymentType === PaymentType.REIMBURSEMENT
			) {
				// For now, treat as earnings - in future might add REIMBURSEMENT type
				itemType = PayslipItemType.EARNING;
			}

			items.push({
				description: templateComp.componentName || paymentComp.name,
				amount: new Prisma.Decimal(componentAmount),
				type: itemType,
				kod: (templateComp.componentCode as PayslipItemKod) || undefined,
				percentage: templateComp.percentage
					? new Prisma.Decimal(templateComp.percentage)
					: undefined,
				paymentComponent: {
					connect: { id: paymentComp.id },
				},
			});

			if (itemType === PayslipItemType.EARNING) {
				totalEarnings += componentAmount;
			}
		}
	}

	// Add other transactions
	for (const tx of transactions) {
		const amount = Number(tx.amount ?? 0);
		const isDeduction = amount < 0;

		items.push({
			description: tx.description ?? "תנועה",
			amount: new Prisma.Decimal(Math.abs(amount)),
			type: isDeduction ? PayslipItemType.DEDUCTION : PayslipItemType.EARNING,
			rate: tx.rate ? new Prisma.Decimal(tx.rate) : undefined,
			units: tx.quantity ? new Prisma.Decimal(tx.quantity) : undefined,
			percentage: tx.percentage ? new Prisma.Decimal(tx.percentage) : undefined,
			kod: tx.componentCode || undefined,
		});

		if (isDeduction) {
			totalDeductions += Math.abs(amount);
		} else {
			totalEarnings += amount;
		}
	}

	// Calculate gross pay
	const grossPay = totalEarnings;

	// Calculate income tax
	const form101 = await db.form101.findFirst({
		where: {
			employeeId,
			taxYear: year,
			status: "SIGNED",
		},
	});

	const taxCredits = form101?.additionalCreditPoints
		? Number(form101.additionalCreditPoints) + 2.25
		: 2.25;

	const taxCalc = calculateIncomeTax(
		grossPay,
		taxCredits,
		form101?.isMainEmployer ?? true,
		form101?.exemptionPercentage ? Number(form101.exemptionPercentage) : 0,
	);

	if (taxCalc.finalTax > 0) {
		items.push({
			description: "מס הכנסה",
			amount: new Prisma.Decimal(taxCalc.finalTax),
			type: PayslipItemType.DEDUCTION,
			kod: PayslipItemKod.K_TAX,
		});
		totalDeductions += taxCalc.finalTax;
	}

	// Calculate National Insurance
	const age = employee.birthDate
		? new Date().getFullYear() - new Date(employee.birthDate).getFullYear()
		: 30;

	const niCalc = calculateNationalInsurance(grossPay, !employee.isForeign, age);

	if (niCalc.employeeNI > 0) {
		items.push({
			description: "ביטוח לאומי - עובד",
			amount: new Prisma.Decimal(niCalc.employeeNI),
			type: PayslipItemType.DEDUCTION,
			kod: PayslipItemKod.K_NI_EMP,
		});
		totalDeductions += niCalc.employeeNI;
	}

	if (niCalc.healthInsurance > 0) {
		items.push({
			description: "ביטוח בריאות",
			amount: new Prisma.Decimal(niCalc.healthInsurance),
			type: PayslipItemType.DEDUCTION,
			kod: PayslipItemKod.K_NI_HEALTH,
		});
		totalDeductions += niCalc.healthInsurance;
	}

	// Apply deduction components
	for (const deductionComp of deductionComponents) {
		let deductionAmount = 0;

		if (deductionComp.percentageOfSalary) {
			deductionAmount =
				(grossPay * Number(deductionComp.percentageOfSalary)) / 100;
		}

		// Apply specific deduction rules based on type
		switch (deductionComp.deductionType) {
			case DeductionType.PENSION:
				if (deductionComp.affectsPension && deductionAmount > 0) {
					items.push({
						description: deductionComp.name,
						amount: new Prisma.Decimal(deductionAmount),
						type: PayslipItemType.DEDUCTION,
						kod: PayslipItemKod.K_PENSION_EMP,
						deductionComponent: {
							connect: { id: deductionComp.id },
						},
					});
					totalDeductions += deductionAmount;
				}
				break;
			case DeductionType.EDUCATION_FUND:
				if (deductionAmount > 0) {
					items.push({
						description: deductionComp.name,
						amount: new Prisma.Decimal(deductionAmount),
						type: PayslipItemType.DEDUCTION,
						kod: PayslipItemKod.K_0121,
						deductionComponent: {
							connect: { id: deductionComp.id },
						},
					});
					totalDeductions += deductionAmount;
				}
				break;
			// Add more deduction types as needed
		}
	}

	// Calculate foreign worker deductions if applicable
	if (employee.isForeign && employee.sector) {
		const foreignDeductions = calculateForeignWorkerDeductions(
			employee.country || "UNKNOWN",
			employee.sector,
			grossPay,
			undefined,
		);

		if (foreignDeductions.housingDeduction > 0) {
			items.push({
				description: "ניכוי דיור",
				amount: new Prisma.Decimal(foreignDeductions.housingDeduction),
				type: PayslipItemType.DEDUCTION,
				kod: PayslipItemKod.K_0400,
			});
			totalDeductions += foreignDeductions.housingDeduction;
		}

		if (foreignDeductions.transportDeduction > 0) {
			items.push({
				description: "ניכוי נסיעות",
				amount: new Prisma.Decimal(foreignDeductions.transportDeduction),
				type: PayslipItemType.DEDUCTION,
				kod: PayslipItemKod.K_0500,
			});
			totalDeductions += foreignDeductions.transportDeduction;
		}
	}

	// Add employer contributions (not deducted from employee)
	if (niCalc.employerNI > 0) {
		items.push({
			description: "ביטוח לאומי - מעסיק",
			amount: new Prisma.Decimal(niCalc.employerNI),
			type: PayslipItemType.EMPLOYER_CONTRIB,
			kod: PayslipItemKod.K_0312,
		});
	}

	// Add employer pension contributions
	const pensionEmployerDeduction = deductionComponents.find(
		(dc) =>
			dc.deductionType === DeductionType.PENSION && dc.name.includes("מעסיק"),
	);

	if (pensionEmployerDeduction?.percentageOfSalary) {
		const pensionEmployerAmount =
			(grossPay * Number(pensionEmployerDeduction.percentageOfSalary)) / 100;
		items.push({
			description: "פנסיה - מעסיק",
			amount: new Prisma.Decimal(pensionEmployerAmount),
			type: PayslipItemType.EMPLOYER_CONTRIB,
			kod: PayslipItemKod.K_0313,
		});
	}

	// Calculate net pay
	const netPay = grossPay - totalDeductions;

	// Validate calculations
	const validation = validatePayslipConsistency(
		grossPay,
		netPay,
		taxCalc.finalTax,
		niCalc.employeeNI + niCalc.healthInsurance,
		totalDeductions -
			taxCalc.finalTax -
			niCalc.employeeNI -
			niCalc.healthInsurance,
		0,
	);

	if (!validation.isConsistent) {
		console.warn("Payslip calculation validation warnings:", validation.errors);
	}

	// Sum pension and severance related items
	const {
		pensionEmployee: pensionEmployeeTotal,
		pensionEmployer: pensionEmployerTotal,
		severancePay: severancePayTotal,
	} = sumContributionTotals(items);


	// Create payslip with items in a transaction
	const payslip = await db.$transaction(async (tx) => {
		// Check if payslip already exists
		const existing = await tx.payslip.findFirst({
			where: {
				employeeId,
				year,
				month,
			},
		});

		if (existing) {
			throw new TRPCError({
				code: "CONFLICT",
				message: `תלוש שכר כבר קיים לתקופה ${month}/${year}`,
			});
		}

		const p = await tx.payslip.create({
			data: {
				tenantId: employee.tenantId,
				employeeId,
				year,
				month,
				periodStart: new Date(year, month - 1, 1),
				periodEnd: new Date(year, month, 0),
				status: "CALCULATED",
				grossPay: new Prisma.Decimal(grossPay),
				netPay: new Prisma.Decimal(netPay),
				taxDeducted: new Prisma.Decimal(taxCalc.finalTax),
				insuranceDeducted: new Prisma.Decimal(
					niCalc.employeeNI + niCalc.healthInsurance,
				),
				otherDeductions: new Prisma.Decimal(
					totalDeductions -
						taxCalc.finalTax -
						niCalc.employeeNI -
						niCalc.healthInsurance,
				),
				allowances: new Prisma.Decimal(0),
				healthInsurance: new Prisma.Decimal(niCalc.healthInsurance),
				pensionEmployee: new Prisma.Decimal(pensionEmployeeTotal),
				pensionEmployer: new Prisma.Decimal(pensionEmployerTotal),
				severancePay: new Prisma.Decimal(severancePayTotal),
				items: { create: items },
			},
			include: {
				items: true,
				employee: true,
			},
		});

		// Mark transactions as processed
		if (transactions.length > 0) {
			await tx.salaryTransaction.updateMany({
				where: { id: { in: transactions.map((t) => t.id) } },
				data: { isProcessed: true },
			});
		}

		return p;
	});

	return payslip;
}

// Helper function to calculate formula values
async function calculateFormulaValue(
	formula: { formulaCode?: string | null },
	context: {
		baseSalary: number;
		hourlyRate: number;
		workDays: number;
		workHours: number;
		valueMap: Map<string, unknown>;
		employee: unknown;
		db: PrismaClient;
		month: number;
		year: number;
	},
): Promise<number> {
	const { baseSalary, hourlyRate, workDays, workHours, valueMap } = context;

	let expression = formula.formulaCode || "";

	// Convert placeholders like {baseSalary} to variable names
	expression = expression.replace(/\{([^}]+)\}/g, "$1");

	const variables: Record<string, number> = {
		baseSalary,
		hourlyRate,
		workDays,
		workHours,
	};

	// Include referenced value components with placeholder value
	valueMap.forEach((_val, code) => {
		variables[code] = 100; // Placeholder until real value logic is implemented
	});

	try {
		const parser = new Parser();
		const parsed = parser.parse(expression);
		const result = parsed.evaluate(variables);
		if (typeof result !== "number" || Number.isNaN(result)) {
			throw new Error("Invalid formula result");
		}
		return result;
	} catch (error) {
		console.error("Formula evaluation error:", error);
		throw new TRPCError({ code: "BAD_REQUEST", message: "שגיאה בנוסחת חישוב" });
	}
}

export async function recalculatePayslip(
	db: PrismaClient,
	payslipId: string,
	workHours?: WorkHoursInput,
) {
	// Get existing payslip
	const existingPayslip = await db.payslip.findUnique({
		where: { id: payslipId },
		include: { items: true },
	});

	if (!existingPayslip) {
		throw new TRPCError({ code: "NOT_FOUND", message: "תלוש שכר לא נמצא" });
	}

	if (
		existingPayslip.status !== "DRAFT" &&
		existingPayslip.status !== "CALCULATED"
	) {
		throw new TRPCError({
			code: "PRECONDITION_FAILED",
			message: "לא ניתן לחשב מחדש תלוש שכר שכבר אושר או שולם",
		});
	}

	// Delete old items and recalculate
	await db.$transaction(async (tx) => {
		// Delete existing items
		await tx.payslipItem.deleteMany({
			where: { payslipId },
		});

		// Delete the payslip itself
		await tx.payslip.delete({
			where: { id: payslipId },
		});

		// Mark related transactions as unprocessed
		await tx.salaryTransaction.updateMany({
			where: {
				employeeId: existingPayslip.employeeId,
				periodMonth: existingPayslip.month,
				periodYear: existingPayslip.year,
				isProcessed: true,
			},
			data: { isProcessed: false },
		});
	});

	// Recalculate
	return calculatePayslip(
		db,
		{
			employeeId: existingPayslip.employeeId,
			month: existingPayslip.month,
			year: existingPayslip.year,
		},
		workHours,
	);
}

export { calculateFormulaValue };

import { z } from "zod";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import type { Prisma } from "@prisma/client";

// Input validation schemas
const createMovementTypeSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  category: z.enum(["CHECK_IN", "CHECK_OUT", "BREAK_START", "BREAK_END", "OTHER"]),
  isDefault: z.boolean().default(false),
  requiresApproval: z.boolean().default(false),
  color: z.string().optional(),
  icon: z.string().optional(),
});

const updateMovementTypeSchema = createMovementTypeSchema.extend({
  id: z.string(),
});

export const movementTypeRouter = createTRPCRouter({
  // Get all movement types
  getAll: publicProcedure
    .input(
      z.object({
        category: z.enum(["CHECK_IN", "CHECK_OUT", "BREAK_START", "BREAK_END", "OTHER"]).optional(),
      }).optional()
    )
    .query(async ({ ctx, input }) => {
      const where: Prisma.MovementTypeWhereInput = { deletedAt: null };
      if (input?.category) {
        where.category = input.category;
      }
      
      return ctx.db.movementType.findMany({
        where,
        orderBy: [
          { category: "asc" },
          { isDefault: "desc" },
          { name: "asc" },
        ],
      });
    }),

  // Get movement type by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const type = await ctx.db.movementType.findUnique({
        where: { id: input.id },
      });

      if (!type || type.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "סוג תנועה לא נמצא",
        });
      }

      return type;
    }),
  // Create new movement type
  create: protectedProcedure
    .input(createMovementTypeSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if code already exists
      const existing = await ctx.db.movementType.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד סוג תנועה כבר קיים במערכת",
        });
      }

      // Check if trying to create another default for the same category
      if (input.isDefault) {
        const existingDefault = await ctx.db.movementType.findFirst({
          where: {
            category: input.category,
            isDefault: true,
            deletedAt: null,
          },
        });

        if (existingDefault) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "כבר קיים סוג תנועה ברירת מחדל לקטגוריה זו",
          });        }
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      
      return ctx.db.movementType.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update movement type
  update: publicProcedure
    .input(updateMovementTypeSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input;

      // Check if type exists
      const existing = await ctx.db.movementType.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "סוג תנועה לא נמצא",
        });
      }

      // Check if new code conflicts with another type
      if (data.code !== existing.code) {
        const codeExists = await ctx.db.movementType.findFirst({
          where: {
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "קוד סוג תנועה כבר קיים במערכת",
          });
        }
      }

      // Check if trying to set as default when another default exists
      if (data.isDefault && !existing.isDefault) {
        const existingDefault = await ctx.db.movementType.findFirst({
          where: {
            category: data.category,
            isDefault: true,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (existingDefault) {
          // Unset the existing default
          await ctx.db.movementType.update({
            where: { id: existingDefault.id },
            data: { isDefault: false },
          });
        }
      }

      return ctx.db.movementType.update({
        where: { id },
        data,
      });
    }),

  // Delete movement type (soft delete)
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Check if type exists
      const existing = await ctx.db.movementType.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "סוג תנועה לא נמצא",
        });
      }

      // Check if type is default
      if (existing.isDefault) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק סוג תנועה שהוא ברירת מחדל",
        });
      }

      // Check if type is in use
      const inUse = await ctx.db.movement.findFirst({
        where: {
          typeId: input.id,
          deletedAt: null,
        },
      });

      if (inUse) {
        throw new TRPCError({
          code: "PRECONDITION_FAILED",
          message: "לא ניתן למחוק סוג תנועה שנמצא בשימוש",
        });
      }

      return ctx.db.movementType.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),
}); 
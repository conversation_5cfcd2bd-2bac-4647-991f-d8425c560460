import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { Prisma } from "@prisma/client";

export const insuranceRateSchema = z.object({
  employeeRate: z.number(),
  employerRate: z.number(),
  description: z.string().optional(),
});

export const insuranceRatesSchema = z.object({
  nationalInsurance: insuranceRateSchema,
  healthInsurance: insuranceRateSchema,
  pension: insuranceRateSchema,
});

export type InsuranceRate = z.infer<typeof insuranceRateSchema>;
export type InsuranceRates = z.infer<typeof insuranceRatesSchema>;

// Default insurance rates for Israel
const DEFAULT_INSURANCE_RATES: InsuranceRates = {
  nationalInsurance: {
    employeeRate: 3.49,
    employerRate: 3.55,
    description: "ביטוח לאומי",
  },
  healthInsurance: {
    employeeRate: 3.1,
    employerRate: 0,
    description: "ביטוח בריאות",
  },
  pension: {
    employeeRate: 6,
    employerRate: 6.5,
    description: "פנסיה חובה",
  },
};

export const insuranceRatesRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(z.object({}).optional())
    .query(async ({ ctx }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }

      // Try to get custom rates from settings
      const setting = await ctx.db.setting.findUnique({
        where: {
          tenantId_key: { tenantId: user.tenantId, key: "INSURANCE_RATES" },
        },
      });

      // Return custom rates if they exist, otherwise return default rates
      if (setting) {
        // Parse and validate the data with Zod
        try {
          const parsed = insuranceRatesSchema.parse(setting.value);
          return { insuranceRates: parsed };
        } catch (error) {
          console.error("Invalid insurance rates format in database:", error);
        }
      }

      // If no custom rates or invalid format, create default settings in DB and return defaults
      await ctx.db.setting.upsert({
        where: {
          tenantId_key: { tenantId: user.tenantId, key: "INSURANCE_RATES" },
        },
        update: {
          value: DEFAULT_INSURANCE_RATES as Prisma.JsonObject,
        },
        create: {
          tenantId: user.tenantId,
          key: "INSURANCE_RATES",
          value: DEFAULT_INSURANCE_RATES as Prisma.JsonObject,
          description: "שיעורי ביטוח לאומי, ביטוח בריאות ופנסיה",
        },
      });

      return { insuranceRates: DEFAULT_INSURANCE_RATES };
    }),

  update: protectedProcedure
    .input(
      z.object({
        nationalInsurance: insuranceRateSchema.optional(),
        healthInsurance: insuranceRateSchema.optional(),
        pension: insuranceRateSchema.optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true, role: true },
      });

      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }

      // Get current rates
      const currentSetting = await ctx.db.setting.findUnique({
        where: {
          tenantId_key: { tenantId: user.tenantId, key: "INSURANCE_RATES" },
        },
      });

      let currentRates = DEFAULT_INSURANCE_RATES;
      if (currentSetting) {
        try {
          currentRates = insuranceRatesSchema.parse(currentSetting.value);
        } catch (error) {
          console.error("Invalid insurance rates format in database:", error);
        }
      }
      
      // Update with new values
      const updatedRates: InsuranceRates = {
        nationalInsurance: input.nationalInsurance || currentRates.nationalInsurance,
        healthInsurance: input.healthInsurance || currentRates.healthInsurance,
        pension: input.pension || currentRates.pension,
      };

      // Save updated rates
      const setting = await ctx.db.setting.upsert({
        where: {
          tenantId_key: { tenantId: user.tenantId, key: "INSURANCE_RATES" },
        },
        update: { 
          value: updatedRates as Prisma.JsonObject,
        },
        create: {
          tenantId: user.tenantId,
          key: "INSURANCE_RATES",
          value: updatedRates as Prisma.JsonObject,
          description: "שיעורי ביטוח לאומי, ביטוח בריאות ופנסיה",
        },
      });

      // Parse and validate return value
      const returnValue = insuranceRatesSchema.parse(setting.value);
      return { insuranceRates: returnValue };
    }),
}); 
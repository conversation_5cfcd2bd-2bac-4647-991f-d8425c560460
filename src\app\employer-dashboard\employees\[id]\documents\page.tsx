  "use client";

  import { useParams } from "next/navigation";
  import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
  import { Skeleton } from "@/components/ui/skeleton";
  import { FileText, ArrowRight } from "lucide-react";
  import Link from "next/link";
  import { EmployeeDocumentsManager } from "../../../components/employee-documents-manager";
  import { useEmployeeDetails } from "../../../hooks";

  export default function EmployeeDocumentsPage() {
    const params = useParams();
    const employeeId = params.id as string;

    const { employee, isLoading, error } = useEmployeeDetails(employeeId);

    if (isLoading) {
      return (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-6 w-48" />
              </div>
            </CardHeader>
          </Card>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Skeleton className="h-8 w-8" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                      <Skeleton className="h-3 w-1/4" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    if (error || !employee) {
      return (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                שגיאה בטעינת פרטי העובד
              </h3>
              <p className="text-gray-500 mb-4">
                לא ניתן לטעון את פרטי העובד. אנא נסה שוב מאוחר יותר.
              </p>
              <Link
                href="/employer-dashboard/employees"
                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700"
              >
                <ArrowRight className="h-4 w-4" />
                חזור לרשימת העובדים
              </Link>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <div className="space-y-6">
        {/* Breadcrumb */}
        <nav className="flex items-center gap-2 text-sm text-gray-600">
          <Link 
            href="/employer-dashboard/employees" 
            className="hover:text-gray-900"
          >
            עובדים
          </Link>
          <span>/</span>
          <Link 
            href={`/employer-dashboard/employees/${employeeId}`}
            className="hover:text-gray-900"
          >
            {employee.name}
          </Link>
          <span>/</span>
          <span className="text-gray-900 font-medium">מסמכים</span>
        </nav>

        {/* Employee Documents Manager */}
        <EmployeeDocumentsManager
          employeeId={employeeId}
          employeeName={employee.name}
          employeeNationalId={employee.id}
        />
      </div>
    );
  } 
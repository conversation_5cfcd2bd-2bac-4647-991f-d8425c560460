// @vitest-environment jsdom

import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, expect, it } from "vitest";
import SalaryPage from "./page";

describe("SalaryPage", () => {
	it("calculates net salary according to tax rate", async () => {
		render(<SalaryPage />);
		await userEvent.clear(screen.getByLabelText("שנה"));
		await userEvent.type(screen.getByLabelText("שנה"), "2025");
		await userEvent.selectOptions(screen.getByLabelText("חודש"), ["3"]);
		await userEvent.clear(screen.getByLabelText("ברוטו"));
		await userEvent.type(screen.getByLabelText("ברוטו"), "10000");

		expect(screen.getByText(/נטו:/).textContent).toContain("8800");
	});
});

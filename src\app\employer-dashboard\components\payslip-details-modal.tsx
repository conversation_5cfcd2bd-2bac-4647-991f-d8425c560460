"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit2, Save, X, Download, Send, FileText, CreditCard } from "lucide-react";
import { toast } from "sonner";
import { type Payslip, type PayslipItem, PayslipItemType, PayslipStatus } from "@prisma/client";
import type { Decimal } from "@prisma/client/runtime/library";

interface PayslipWithItems extends Payslip {
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    nationalId: string;
  };
  items: PayslipItem[];
}

interface PayslipDetailsModalProps {
  payslip: PayslipWithItems | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdate?: (payslipId: string, updates: {
    grossPay: number;
    netPay: number;
    taxDeducted: number;
    insuranceDeducted: number;
    otherDeductions?: number | null;
    allowances?: number | null;
  }) => Promise<void>;
  onApprove?: (payslipId: string) => Promise<void>;
  onGeneratePDF?: (payslipId: string) => Promise<void>;
  onSendToEmployee?: (payslipId: string) => Promise<void>;
  onApproveAndPay?: (payslipId: string) => Promise<void>;
}

export function PayslipDetailsModal({
  payslip,
  isOpen,
  onClose,
  onUpdate,
  onApprove,
  onGeneratePDF,
  onSendToEmployee,
  onApproveAndPay
}: PayslipDetailsModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedPayslip, setEditedPayslip] = useState<PayslipWithItems | null>(payslip);
  const [isLoading, setIsLoading] = useState(false);

  // Update edited payslip when payslip prop changes
  useEffect(() => {
    if (payslip) {
      setEditedPayslip(payslip);
    }
  }, [payslip]);

  if (!payslip || !editedPayslip) return null;

  const canEdit = payslip.status === PayslipStatus.DRAFT || payslip.status === PayslipStatus.CALCULATED;
  const canApprove = payslip.status === PayslipStatus.CALCULATED && !isEditing;
  const canGeneratePDF = payslip.status === PayslipStatus.APPROVED || payslip.status === PayslipStatus.PAID;
  const canApproveAndPay = payslip.status === PayslipStatus.DRAFT || payslip.status === PayslipStatus.CALCULATED;

  const handleSave = async () => {
    if (!onUpdate) return;

    setIsLoading(true);
    try {
      await onUpdate(payslip.id, {
        grossPay: Number(editedPayslip.grossPay),
        netPay: Number(editedPayslip.netPay),
        taxDeducted: Number(editedPayslip.taxDeducted),
        insuranceDeducted: Number(editedPayslip.insuranceDeducted),
        otherDeductions: editedPayslip.otherDeductions ? Number(editedPayslip.otherDeductions) : null,
        allowances: editedPayslip.allowances ? Number(editedPayslip.allowances) : null,
      });
      setIsEditing(false);
      toast.success("תלוש השכר עודכן בהצלחה");
    } catch (error) {
      toast.error("שגיאה בעדכון תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!onApprove) return;

    setIsLoading(true);
    try {
      await onApprove(payslip.id);
      toast.success("תלוש השכר אושר בהצלחה");
    } catch (error) {
      toast.error("שגיאה באישור תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePDF = async () => {
    if (!onGeneratePDF) return;

    setIsLoading(true);
    try {
      await onGeneratePDF(payslip.id);
      toast.success("PDF נוצר בהצלחה");
    } catch (error) {
      toast.error("שגיאה ביצירת PDF");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendToEmployee = async () => {
    if (!onSendToEmployee) return;

    setIsLoading(true);
    try {
      await onSendToEmployee(payslip.id);
      toast.success("תלוש השכר נשלח לעובד בהצלחה");
    } catch (error) {
      toast.error("שגיאה בשליחת תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveAndPay = async () => {
    if (!onApproveAndPay) return;

    setIsLoading(true);
    try {
      await onApproveAndPay(payslip.id);
      toast.success("תלוש השכר אושר, שולם ונשלח בהצלחה");
    } catch (error) {
      toast.error("שגיאה באישור ותשלום תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number | string | null | undefined | Decimal) => {
    if (amount === null || amount === undefined || amount === "") return "₪0";
    const num = typeof amount === "string" ? parseFloat(amount) : Number(amount as unknown as Decimal);
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(num);
  };

  // Get period description (Month Year)
  const getPeriod = () => {
    const months = [
      "ינואר", "פברואר", "מרץ", "אפריל", "מאי", "יוני",
      "יולי", "אוגוסט", "ספטמבר", "אוקטובר", "נובמבר", "דצמבר"
    ];
    return `${months[payslip.month - 1]} ${payslip.year}`;
  };

  // Filter payslip items by type
  const earnings = payslip.items.filter(item => item.type === PayslipItemType.EARNING);
  const deductions = payslip.items.filter(item => item.type === PayslipItemType.DEDUCTION);
  const employerContribs = payslip.items.filter(item => item.type === PayslipItemType.EMPLOYER_CONTRIB);
  const reimbursements = payslip.items.filter(item => item.type === PayslipItemType.REIMBURSEMENT);

  // Helper to update Decimal fields
  const updateDecimalField = (field: keyof PayslipWithItems, value: number | string) => {
    const newPayslip = { ...editedPayslip };
    const parsed = typeof value === "string" ? parseFloat(value) : value;
    // @ts-expect-error - We know this is a Decimal field
    newPayslip[field] = parsed as unknown as Decimal;
    setEditedPayslip(newPayslip);
  };

  // Helper to update nullable Decimal fields
  const updateNullableDecimalField = (field: keyof PayslipWithItems, value: string | number) => {
    const newPayslip = { ...editedPayslip };
    const isEmpty = value === "" || value === null || value === undefined;
    // @ts-expect-error - We know this is a nullable Decimal field
    newPayslip[field] = isEmpty ? null : (typeof value === "string" ? parseFloat(value) : value) as unknown as Decimal;
    setEditedPayslip(newPayslip);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>תלוש שכר - {getPeriod()}</span>
            <div className="flex gap-2">
              {canEdit && !isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  disabled={isLoading}
                >
                  <Edit2 className="h-4 w-4 ml-2" />
                  עריכה
                </Button>
              )}
              {isEditing && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsEditing(false);
                      setEditedPayslip(payslip);
                    }}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4 ml-2" />
                    ביטול
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={isLoading}
                  >
                    <Save className="h-4 w-4 ml-2" />
                    שמירה
                  </Button>
                </>
              )}
              {canApproveAndPay && !isEditing && (
                <Button
                  size="sm"
                  onClick={handleApproveAndPay}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CreditCard className="h-4 w-4 ml-2" />
                  אישור, תשלום ושליחת SMS
                </Button>
              )}
              {canApprove && (
                <Button
                  size="sm"
                  onClick={handleApprove}
                  disabled={isLoading}
                >
                  <FileText className="h-4 w-4 ml-2" />
                  אישור
                </Button>
              )}
              {canGeneratePDF && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleGeneratePDF}
                    disabled={isLoading}
                  >
                    <Download className="h-4 w-4 ml-2" />
                    PDF
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSendToEmployee}
                    disabled={isLoading}
                  >
                    <Send className="h-4 w-4 ml-2" />
                    שלח לעובד
                  </Button>
                </>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Employee Info */}
          <div className="bg-muted/30 p-4 rounded-md">
            <h3 className="font-semibold mb-2">פרטי עובד</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">שם מלא:</span>{" "}
                <span>{payslip.employee.firstName} {payslip.employee.lastName}</span>
              </div>
              <div>
                <span className="font-medium">מספר זהות:</span>{" "}
                <span>{payslip.employee.nationalId}</span>
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">סיכום</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between items-center">
                  <span>ברוטו:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={Number(editedPayslip.grossPay)}
                      onChange={(e) => updateDecimalField('grossPay', e.target.valueAsNumber)}
                      className="w-24 h-6 text-xs"
                    />
                  ) : (
                    <span className="font-medium">{formatCurrency(payslip.grossPay)}</span>
                  )}
                </div>
                <div className="flex justify-between items-center">
                  <span>מס הכנסה:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={Number(editedPayslip.taxDeducted)}
                      onChange={(e) => updateDecimalField('taxDeducted', e.target.valueAsNumber)}
                      className="w-24 h-6 text-xs"
                    />
                  ) : (
                    <span className="font-medium">{formatCurrency(payslip.taxDeducted)}</span>
                  )}
                </div>
                <div className="flex justify-between items-center">
                  <span>ביטוח לאומי:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={Number(editedPayslip.insuranceDeducted)}
                      onChange={(e) => updateDecimalField('insuranceDeducted', e.target.valueAsNumber)}
                      className="w-24 h-6 text-xs"
                    />
                  ) : (
                    <span className="font-medium">{formatCurrency(payslip.insuranceDeducted)}</span>
                  )}
                </div>
                <div className="flex justify-between items-center">
                  <span>קופת גמל/פנסיה:</span>
                  <span className="font-medium">{formatCurrency(payslip.pensionEmployee)}</span>
                </div>
                {(editedPayslip.otherDeductions || isEditing) && (
                  <div className="flex justify-between items-center">
                    <span>ניכויים אחרים:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={editedPayslip.otherDeductions ? Number(editedPayslip.otherDeductions) : ''}
                        onChange={(e) => updateNullableDecimalField('otherDeductions', e.target.value)}
                        className="w-24 h-6 text-xs"
                      />
                    ) : (
                      <span className="font-medium">{formatCurrency(payslip.otherDeductions)}</span>
                    )}
                  </div>
                )}
                {(editedPayslip.allowances || isEditing) && (
                  <div className="flex justify-between items-center">
                    <span>קצבאות:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={editedPayslip.allowances ? Number(editedPayslip.allowances) : ''}
                        onChange={(e) => updateNullableDecimalField('allowances', e.target.value)}
                        className="w-24 h-6 text-xs"
                      />
                    ) : (
                      <span className="font-medium">{formatCurrency(payslip.allowances)}</span>
                    )}
                  </div>
                )}
                <Separator className="my-2" />
                <div className="flex justify-between items-center font-semibold">
                  <span>נטו:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={Number(editedPayslip.netPay)}
                      onChange={(e) => updateDecimalField('netPay', e.target.valueAsNumber)}
                      className="w-24 h-6 text-xs font-semibold"
                    />
                  ) : (
                    <span>{formatCurrency(payslip.netPay)}</span>
                  )}
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">פרטים</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>סטטוס:</span>
                  <Badge 
                    variant={
                      payslip.status === PayslipStatus.PAID || 
                      payslip.status === PayslipStatus.APPROVED || 
                      payslip.status === PayslipStatus.SENT ? 
                        "default" : "secondary"
                    }
                  >
                    {payslip.status === PayslipStatus.DRAFT ? "טיוטה" :
                     payslip.status === PayslipStatus.CALCULATED ? "מחושב" :
                     payslip.status === PayslipStatus.APPROVED ? "מאושר" :
                     payslip.status === PayslipStatus.SENT ? "נשלח" :
                     payslip.status === PayslipStatus.PAID ? "שולם" : "מבוטל"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>תאריך הפקה:</span>
                  <span>{new Date(payslip.issuedAt).toLocaleDateString("he-IL")}</span>
                </div>
                {payslip.approvedAt && (
                  <div className="flex justify-between">
                    <span>תאריך אישור:</span>
                    <span>{new Date(payslip.approvedAt).toLocaleDateString("he-IL")}</span>
                  </div>
                )}
                {payslip.periodStart && payslip.periodEnd && (
                  <div className="flex justify-between">
                    <span>תקופת שכר:</span>
                    <span>
                      {new Date(payslip.periodStart).toLocaleDateString("he-IL")} - {" "}
                      {new Date(payslip.periodEnd).toLocaleDateString("he-IL")}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Earnings */}
          {earnings.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">תשלומים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">כמות</TableHead>
                    <TableHead className="text-right">תעריף</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {earnings.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{item.units ? Number(item.units).toString() : "-"}</TableCell>
                      <TableCell>{item.rate ? formatCurrency(item.rate) : "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Deductions */}
          {deductions.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">ניכויים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">אחוז</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deductions.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{item.percentage ? `${Number(item.percentage)}%` : "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Reimbursements */}
          {reimbursements.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">החזרים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reimbursements.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Employer Contributions */}
          {employerContribs.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">הפרשות מעסיק</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">אחוז</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employerContribs.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.percentage ? `${Number(item.percentage)}%` : "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Show component relationships if they exist */}
          {payslip.items.some(item => item.paymentComponentId || item.deductionComponentId) && (
            <div className="mt-6 pt-4 border-t">
              <h3 className="font-semibold mb-2">פרטים נוספים</h3>
              <p className="text-sm text-muted-foreground">
                התלוש כולל רכיבי שכר מוגדרים במערכת הכוללים: הפרשות פנסיה, קופות גמל, ביטוח לאומי וניכויי מס
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
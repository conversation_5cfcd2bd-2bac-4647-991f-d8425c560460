"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON>A<PERSON>s, YAxis } from "recharts";

const data = [
	{ name: "ינו<PERSON><PERSON>", value: 4000 },
	{ name: "פברו<PERSON><PERSON>", value: 3000 },
	{ name: "מרץ", value: 2000 },
	{ name: "אפרי<PERSON>", value: 2780 },
	{ name: "מאי", value: 1890 },
];

export default function ReportsPage() {
	const [records] = useState(data);

	return (
		<div className="container mx-auto space-y-6 py-6" dir="rtl">
			<div>
				<h1 className="font-bold text-2xl">דוחות</h1>
				<p className="text-muted-foreground">סטטיסטיקות חודשיות לדוגמה</p>
			</div>
			<Card>
				<CardHeader>
					<CardTitle>הוצאות שכר</CardTitle>
				</Card<PERSON><PERSON>er>
				<CardContent>
					<div className="h-72 w-full">
						<ResponsiveContainer width="100%" height="100%">
							<BarChart data={records}>
								<XAxis dataKey="name" stroke="#888" />
								<YAxis stroke="#888" />
								<Bar dataKey="value" fill="#0d9488" radius={[4, 4, 0, 0]} />
							</BarChart>
						</ResponsiveContainer>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

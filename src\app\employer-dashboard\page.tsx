"use client";


import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { format, addMonths, subMonths, startOfMonth, endOfMonth } from "date-fns";
import { he } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertCircle,
  Calendar,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  CheckCircle,
  Clock,
  Search,
  Filter,
  Download,
  Send,
  BarChart3,
  PieChart,
  Receipt,
  Calculator,
  CreditCard,
} from "lucide-react";
import { PieChart as RechartsChart, Pie, Cell, ResponsiveContainer, Tooltip as RechartsTooltip, LineChart, Line, XAxis, YAxis, CartesianGrid, Legend } from "recharts";
import { api } from "@/trpc/react";
import { toast } from "sonner";
import { PayslipDetailsModal } from "./components/payslip-details-modal";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import type { Payslip } from "@prisma/client";

interface DashboardMetrics {
  totalGross: number;
  totalNet: number;
  totalDeductions: number;
  employeeCount: number;
  previousGross?: number;
  previousNet?: number;
  previousDeductions?: number;
  previousEmployeeCount?: number;
}

interface TaxBracket {
  id?: string;
  minIncome: number;
  maxIncome: number;
  rate: number;
}

interface SalaryComponentData {
  name: string;
  value: number;
  color: string;
}

const HEBREW_MONTHS = [
  "ינואר", "פברואר", "מרץ", "אפריל", "מאי", "יוני",
  "יולי", "אוגוסט", "ספטמבר", "אוקטובר", "נובמבר", "דצמבר"
];

const CHART_COLORS = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#F59E0B", // Amber
  "#EF4444", // Red
  "#8B5CF6", // Purple
  "#EC4899", // Pink
];

const DEFAULT_TAX_BRACKETS_2025: TaxBracket[] = [
  { minIncome: 0, maxIncome: 7010, rate: 0.1 },
  { minIncome: 7010, maxIncome: 10060, rate: 0.14 },
  { minIncome: 10060, maxIncome: 16150, rate: 0.2 },
  { minIncome: 16150, maxIncome: 22440, rate: 0.31 },
  { minIncome: 22440, maxIncome: 46690, rate: 0.35 },
  { minIncome: 46690, maxIncome: 60130, rate: 0.47 },
  { minIncome: 60130, maxIncome: Infinity, rate: 0.5 },
];


export default function EmployerDashboardPage() {
  const { data: session, status } = useSession();
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [selectedPayslip, setSelectedPayslip] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Redirect if not authenticated
  if (status === "loading") {
    return <LoadingDashboard />;
  }

  if (status === "unauthenticated") {
    redirect("/login");
  }

  const employerId = session?.user?.employerId || "";
  const month = selectedDate.getMonth() + 1;
  const year = selectedDate.getFullYear();

  // Fetch payslips data
  const { data: payslipsData, isLoading: isLoadingPayslips, refetch: refetchPayslips } = api.payslip.getAll.useQuery({
    month,
    year,
    limit: 100,
  });

  // Fetch previous month data for comparison
  const previousDate = subMonths(selectedDate, 1);
  const { data: previousPayslipsData } = api.payslip.getAll.useQuery({
    month: previousDate.getMonth() + 1,
    year: previousDate.getFullYear(),
    limit: 100,
  });

  // Fetch tax brackets for display
  const { data: taxBracketsData, isLoading: isLoadingTaxBrackets } = api.taxBracket.getAll.useQuery({ year });

  // tRPC mutations
  const approveMutation = api.payslip.approve.useMutation();
  const updateMutation = api.payslip.update.useMutation();
  const approveAndPayMutation = api.payslip.approveAndPay.useMutation();

  // Fetch tax brackets setting
  const { data: taxBracketSetting } = api.setting.getByKey.useQuery({
    key: "TAX_BRACKETS_2025",
  });

  // Type-safe way to handle the tax brackets setting
  const TAX_BRACKETS = taxBracketSetting?.value 
    ? (taxBracketSetting.value as unknown as TaxBracket[]) ?? DEFAULT_TAX_BRACKETS_2025
    : DEFAULT_TAX_BRACKETS_2025;

  // Fetch insurance rates from tRPC
  const { data: insuranceRatesData, isLoading: isLoadingInsuranceRates } = api.insuranceRates.getAll.useQuery();

  // Calculate metrics
  const metrics: DashboardMetrics = React.useMemo(() => {
    if (!payslipsData?.payslips) {
      return {
        totalGross: 0,
        totalNet: 0,
        totalDeductions: 0,
        employeeCount: 0,
      };
    }

    const current = payslipsData.payslips.reduce(
      (acc, payslip) => ({
        totalGross: acc.totalGross + Number(payslip.grossPay),
        totalNet: acc.totalNet + Number(payslip.netPay),
        totalDeductions: acc.totalDeductions + Number(payslip.taxDeducted) + Number(payslip.insuranceDeducted) + Number(payslip.otherDeductions || 0),
        employeeCount: acc.employeeCount + 1,
      }),
      { totalGross: 0, totalNet: 0, totalDeductions: 0, employeeCount: 0 }
    );

    const previous = previousPayslipsData?.payslips?.reduce(
      (acc, payslip) => ({
        totalGross: acc.totalGross + Number(payslip.grossPay),
        totalNet: acc.totalNet + Number(payslip.netPay),
        totalDeductions: acc.totalDeductions + Number(payslip.taxDeducted) + Number(payslip.insuranceDeducted) + Number(payslip.otherDeductions || 0),
        employeeCount: acc.employeeCount + 1,
      }),
      { totalGross: 0, totalNet: 0, totalDeductions: 0, employeeCount: 0 }
    );

    return {
      ...current,
      previousGross: previous?.totalGross,
      previousNet: previous?.totalNet,
      previousDeductions: previous?.totalDeductions,
      previousEmployeeCount: previous?.employeeCount,
    };
  }, [payslipsData, previousPayslipsData]);

  // Calculate salary components for pie chart
  const salaryComponents: SalaryComponentData[] = React.useMemo(() => {
    if (!payslipsData?.payslips || payslipsData.payslips.length === 0) {
      return [];
    }

    const components = payslipsData.payslips.reduce(
      (acc, payslip) => {
        payslip.items?.forEach((item) => {
          if (item.type === "EARNING") {
            const key = item.description || "אחר";
            if (!acc[key]) acc[key] = 0;
            acc[key] += Number(item.amount);
          }
        });
        return acc;
      },
      {} as Record<string, number>
    );

    return Object.entries(components)
      .map(([name, value], index) => ({
        name,
        value,
        color: CHART_COLORS[index % CHART_COLORS.length]!,
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 6); // Top 6 components
  }, [payslipsData]);

  // Calculate monthly trends (last 6 months)
  const monthlyTrends = React.useMemo(() => {
    const trends = [];
    for (let i = 5; i >= 0; i--) {
      const date = subMonths(selectedDate, i);
      trends.push({
        month: HEBREW_MONTHS[date.getMonth()],
        gross: Math.random() * 50000 + 150000, // Placeholder - replace with actual data
        net: Math.random() * 40000 + 120000,
        deductions: Math.random() * 10000 + 30000,
      });
    }
    return trends;
  }, [selectedDate]);

  // Approval statistics
  const approvalStats = React.useMemo(() => {
    if (!payslipsData?.payslips) {
      return { approved: 0, pending: 0, draft: 0, total: 0 };
    }

    const stats = payslipsData.payslips.reduce(
      (acc, payslip) => {
        acc.total++;
        if (payslip.status === "APPROVED" || payslip.status === "PAID") {
          acc.approved++;
        } else if (payslip.status === "CALCULATED") {
          acc.pending++;
        } else if (payslip.status === "DRAFT") {
          acc.draft++;
        }
        return acc;
      },
      { approved: 0, pending: 0, draft: 0, total: 0 }
    );

    return stats;
  }, [payslipsData]);

  // Filter payslips by search
  const filteredPayslips = React.useMemo(() => {
    if (!payslipsData?.payslips) return [];
    
    return payslipsData.payslips.filter((payslip) => {
      const fullName = `${payslip.employee.firstName} ${payslip.employee.lastName}`.toLowerCase();
      return fullName.includes(searchTerm.toLowerCase());
    });
  }, [payslipsData, searchTerm]);

  // Handlers
  const handleMonthChange = (direction: "prev" | "next" | "current") => {
    if (direction === "current") {
      setSelectedDate(new Date());
    } else if (direction === "prev") {
      setSelectedDate(subMonths(selectedDate, 1));
    } else {
      setSelectedDate(addMonths(selectedDate, 1));
    }
  };

  const handleApproveAll = async () => {
    try {
      const pendingPayslips = payslipsData?.payslips.filter(p => p.status === "CALCULATED") || [];
      
      for (const payslip of pendingPayslips) {
        await approveMutation.mutateAsync(payslip.id);
      }

      toast.success(`${pendingPayslips.length} תלושים אושרו בהצלחה`);
      refetchPayslips();
    } catch (error) {
      toast.error("שגיאה באישור התלושים");
    }
  };

  const toggleRowExpansion = (id: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedRows(newExpanded);
  };

  const handleViewPayslip = async (payslipId: string) => {
    try {
      const payslip = await api.payslip.getById.useQuery({ id: payslipId }).data;
      setSelectedPayslip(payslip);
      setIsModalOpen(true);
    } catch (error) {
      toast.error("שגיאה בטעינת תלוש השכר");
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(amount);
  };

  const calculatePercentageChange = (current: number, previous?: number) => {
    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  return (
    <div className="min-h-screen bg-background p-4 md:p-6 lg:p-8" dir="rtl">
      <TooltipProvider>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mx-auto max-w-7xl space-y-6"
        >
          {/* Header with Month/Year Selection */}
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold">לוח בקרת שכר</h1>
              <p className="text-muted-foreground">ניהול וניתוח תלושי שכר לעובדים</p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleMonthChange("prev")}
                className="h-10 w-10"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>

              <Select
                value={`${year}-${month}`}
                onValueChange={(value) => {
                  const [y, m] = value.split("-").map(Number);
                  setSelectedDate(new Date(y!, m! - 1));
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 24 }, (_, i) => {
                    const date = subMonths(new Date(), i);
                    const value = `${date.getFullYear()}-${date.getMonth() + 1}`;
                    const label = `${HEBREW_MONTHS[date.getMonth()]} ${date.getFullYear()}`;
                    return (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={() => handleMonthChange("next")}
                className="h-10 w-10"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                onClick={() => handleMonthChange("current")}
              >
                <Calendar className="ml-2 h-4 w-4" />
                חודש נוכחי
              </Button>
            </div>
          </div>

          {/* Financial Summary Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="שכר ברוטו"
              value={metrics.totalGross}
              previousValue={metrics.previousGross}
              icon={DollarSign}
              color="blue"
              loading={isLoadingPayslips}
            />
            <MetricCard
              title="שכר נטו"
              value={metrics.totalNet}
              previousValue={metrics.previousNet}
              icon={CreditCard}
              color="green"
              loading={isLoadingPayslips}
            />
            <MetricCard
              title="סה״כ ניכויים"
              value={metrics.totalDeductions}
              previousValue={metrics.previousDeductions}
              icon={Receipt}
              color="red"
              loading={isLoadingPayslips}
            />
            <MetricCard
              title="מספר עובדים"
              value={metrics.employeeCount}
              previousValue={metrics.previousEmployeeCount}
              icon={Users}
              color="purple"
              loading={isLoadingPayslips}
              isCurrency={false}
            />
          </div>

          {/* Charts Row */}
          <div className="grid gap-4 lg:grid-cols-2">
            {/* Pie Chart - Salary Components */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  התפלגות רכיבי שכר
                </CardTitle>
                <CardDescription>
                  חלוקת סוגי התשלומים השונים
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingPayslips ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : salaryComponents.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsChart>
                      <Pie
                        data={salaryComponents}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {salaryComponents.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <RechartsTooltip
                        formatter={(value: number) => formatCurrency(value)}
                        contentStyle={{
                          backgroundColor: "rgba(255, 255, 255, 0.95)",
                          border: "1px solid #e5e7eb",
                          borderRadius: "8px",
                          direction: "rtl",
                        }}
                      />
                    </RechartsChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex h-[300px] items-center justify-center text-muted-foreground">
                    אין נתונים להצגה
                  </div>
                )}
                {salaryComponents.length > 0 && (
                  <div className="mt-4 grid grid-cols-2 gap-2">
                    {salaryComponents.map((component, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: component.color }}
                        />
                        <span className="text-sm">{component.name}</span>
                        <span className="mr-auto text-sm text-muted-foreground">
                          {formatCurrency(component.value)}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Line Chart - Monthly Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  מגמות חודשיות
                </CardTitle>
                <CardDescription>
                  השוואת נתוני שכר ב-6 החודשים האחרונים
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={monthlyTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `₪${(value / 1000).toFixed(0)}K`} />
                    <RechartsTooltip
                      formatter={(value: number) => formatCurrency(value)}
                      contentStyle={{
                        backgroundColor: "rgba(255, 255, 255, 0.95)",
                        border: "1px solid #e5e7eb",
                        borderRadius: "8px",
                        direction: "rtl",
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="gross"
                      stroke="#3B82F6"
                      name="ברוטו"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="net"
                      stroke="#10B981"
                      name="נטו"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="deductions"
                      stroke="#EF4444"
                      name="ניכויים"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Tax Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                מדרגות מס הכנסה {year}
              </CardTitle>
              <CardDescription>
                שיעורי מס לפי מדרגות הכנסה חודשית
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-4">
                {TAX_BRACKETS.map((bracket, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-3"
                  >
                    <div>
                      <p className="text-sm font-medium">
                        {formatCurrency(bracket.minIncome)} - {bracket.maxIncome === Infinity ? "ומעלה" : formatCurrency(bracket.maxIncome)}
                      </p>
                      <p className="text-xs text-muted-foreground">מדרגה {index + 1}</p>

                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-4 grid gap-2 md:grid-cols-3">
                {isLoadingInsuranceRates ? (
                  <>
                    <Skeleton className="h-16 w-full" />
                    <Skeleton className="h-16 w-full" />
                    <Skeleton className="h-16 w-full" />
                  </>
                ) : insuranceRatesData ? (
                  <>
                    <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-950">
                      <p className="text-sm font-medium">{insuranceRatesData.insuranceRates.nationalInsurance.description || "ביטוח לאומי"}</p>
                      <p className="text-xs text-muted-foreground">
                        עובד: {insuranceRatesData.insuranceRates.nationalInsurance.employeeRate}% | 
                        מעסיק: {insuranceRatesData.insuranceRates.nationalInsurance.employerRate}%
                      </p>
                    </div>
                    <div className="rounded-lg bg-green-50 p-3 dark:bg-green-950">
                      <p className="text-sm font-medium">{insuranceRatesData.insuranceRates.healthInsurance.description || "ביטוח בריאות"}</p>
                      <p className="text-xs text-muted-foreground">
                        עובד: {insuranceRatesData.insuranceRates.healthInsurance.employeeRate}% | 
                        מעסיק: {insuranceRatesData.insuranceRates.healthInsurance.employerRate}%
                      </p>
                    </div>
                    <div className="rounded-lg bg-purple-50 p-3 dark:bg-purple-950">
                      <p className="text-sm font-medium">{insuranceRatesData.insuranceRates.pension.description || "פנסיה חובה"}</p>
                      <p className="text-xs text-muted-foreground">
                        עובד: {insuranceRatesData.insuranceRates.pension.employeeRate}% | 
                        מעסיק: {insuranceRatesData.insuranceRates.pension.employerRate}%
                      </p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-950">
                      <p className="text-sm font-medium">ביטוח לאומי</p>
                      <p className="text-xs text-muted-foreground">עובד: 3.49% | מעסיק: 3.55%</p>
                    </div>
                    <div className="rounded-lg bg-green-50 p-3 dark:bg-green-950">
                      <p className="text-sm font-medium">ביטוח בריאות</p>
                      <p className="text-xs text-muted-foreground">עובד: 3.1% | מעסיק: 0%</p>
                    </div>
                    <div className="rounded-lg bg-purple-50 p-3 dark:bg-purple-950">
                      <p className="text-sm font-medium">פנסיה חובה</p>
                      <p className="text-xs text-muted-foreground">עובד: 6% | מעסיק: 6.5%</p>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Approval System */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                מערכת אישורים
              </CardTitle>
              <CardDescription>
                סטטוס אישור תלושי שכר לחודש {HEBREW_MONTHS[month - 1]} {year}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">
                      {approvalStats.approved} מתוך {approvalStats.total} תלושים אושרו
                    </p>
                    <Progress value={(approvalStats.approved / approvalStats.total) * 100} className="h-2" />
                  </div>
                  {approvalStats.pending > 0 && (
                    <Button onClick={handleApproveAll} className="mr-4">
                      <CheckCircle className="ml-2 h-4 w-4" />
                      אשר {approvalStats.pending} תלושים ממתינים
                    </Button>
                  )}
                </div>
                <div className="grid gap-2 md:grid-cols-3">
                  <div className="flex items-center gap-2 rounded-lg border p-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm font-medium">{approvalStats.approved} מאושרים</p>
                      <p className="text-xs text-muted-foreground">תלושים שאושרו או שולמו</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 rounded-lg border p-3">
                    <Clock className="h-5 w-5 text-yellow-500" />
                    <div>
                      <p className="text-sm font-medium">{approvalStats.pending} ממתינים</p>
                      <p className="text-xs text-muted-foreground">ממתינים לאישור</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 rounded-lg border p-3">
                    <AlertCircle className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">{approvalStats.draft} טיוטות</p>
                      <p className="text-xs text-muted-foreground">טרם חושבו</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Employee Salary Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                טבלת שכר עובדים
              </CardTitle>
              <CardDescription>
                פירוט תלושי שכר לכל העובדים
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                <div className="relative max-w-sm">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="חיפוש לפי שם עובד..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="ml-2 h-4 w-4" />
                    סינון
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="ml-2 h-4 w-4" />
                    ייצוא
                  </Button>
                </div>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]"></TableHead>
                      <TableHead>שם עובד</TableHead>
                      <TableHead>ת.ז.</TableHead>
                      <TableHead>ברוטו</TableHead>
                      <TableHead>ניכויים</TableHead>
                      <TableHead>נטו</TableHead>
                      <TableHead>סטטוס</TableHead>
                      <TableHead>פעולות</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <AnimatePresence>
                      {isLoadingPayslips ? (
                        Array.from({ length: 5 }).map((_, i) => (
                          <TableRow key={i}>
                            <TableCell colSpan={8}>
                              <Skeleton className="h-12 w-full" />
                            </TableCell>
                          </TableRow>
                        ))
                      ) : filteredPayslips.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center">
                            אין תלושי שכר להצגה
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredPayslips.map((payslip) => (
                          <React.Fragment key={payslip.id}>
                            <motion.tr
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -20 }}
                              className="group cursor-pointer hover:bg-muted/50"
                              onClick={() => toggleRowExpansion(payslip.id)}
                            >
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleRowExpansion(payslip.id);
                                  }}
                                >
                                  {expandedRows.has(payslip.id) ? (
                                    <ChevronUp className="h-4 w-4" />
                                  ) : (
                                    <ChevronDown className="h-4 w-4" />
                                  )}
                                </Button>
                              </TableCell>
                              <TableCell className="font-medium">
                                {payslip.employee.firstName} {payslip.employee.lastName}
                              </TableCell>
                              <TableCell>{payslip.employee.nationalId}</TableCell>
                              <TableCell>{formatCurrency(Number(payslip.grossPay))}</TableCell>
                              <TableCell>
                                {formatCurrency(
                                  Number(payslip.taxDeducted) +
                                  Number(payslip.insuranceDeducted) +
                                  Number(payslip.otherDeductions || 0)
                                )}
                              </TableCell>
                              <TableCell className="font-medium">
                                {formatCurrency(Number(payslip.netPay))}
                              </TableCell>
                              <TableCell>
                                <PayslipStatusBadge status={payslip.status} />
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleViewPayslip(payslip.id);
                                        }}
                                      >
                                        <FileText className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>הצג תלוש</p>
                                    </TooltipContent>
                                  </Tooltip>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-8 w-8"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          toast.info("שליחת תלוש לעובד");
                                        }}
                                      >
                                        <Send className="h-4 w-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>שלח לעובד</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </div>
                              </TableCell>
                            </motion.tr>
                            <AnimatePresence>
                              {expandedRows.has(payslip.id) && (
                                <motion.tr
                                  initial={{ opacity: 0, height: 0 }}
                                  animate={{ opacity: 1, height: "auto" }}
                                  exit={{ opacity: 0, height: 0 }}
                                >
                                  <TableCell colSpan={8} className="bg-muted/30 p-4">
                                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                      {payslip.items?.slice(0, 8).map((item, index) => (
                                        <div key={index} className="flex justify-between text-sm">
                                          <span className="text-muted-foreground">{item.description}:</span>
                                          <span className={cn(
                                            "font-medium",
                                            item.type === "DEDUCTION" && "text-red-600"
                                          )}>
                                            {formatCurrency(Number(item.amount))}
                                          </span>
                                        </div>
                                      ))}
                                    </div>
                                    {(payslip.items?.length || 0) > 8 && (
                                      <p className="mt-2 text-sm text-muted-foreground">
                                        ועוד {payslip.items!.length - 8} רכיבים נוספים...
                                      </p>
                                    )}
                                  </TableCell>
                                </motion.tr>
                              )}
                            </AnimatePresence>
                          </React.Fragment>
                        ))
                      )}
                    </AnimatePresence>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </TooltipProvider>

      {/* Payslip Details Modal */}
      <PayslipDetailsModal
        payslip={selectedPayslip}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedPayslip(null);
        }}
        onUpdate={async (id, updates) => {
          await updateMutation.mutateAsync({ id, ...updates });
          refetchPayslips();
        }}
        onApprove={async (id) => {
          await approveMutation.mutateAsync(id);
          refetchPayslips();
        }}
        onApproveAndPay={async (id) => {
          await approveAndPayMutation.mutateAsync(id);
          refetchPayslips();
        }}
        onGeneratePDF={async (id) => {
          toast.info("יצירת PDF בפיתוח");
        }}
        onSendToEmployee={async (id) => {
          toast.info("שליחה לעובד בפיתוח");
        }}
      />
    </div>
  );
}

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: number;
  previousValue?: number;
  icon: React.ElementType;
  color: "blue" | "green" | "red" | "purple";
  loading?: boolean;
  isCurrency?: boolean;
}

function MetricCard({
  title,
  value,
  previousValue,
  icon: Icon,
  color,
  loading = false,
  isCurrency = true,
}: MetricCardProps) {
  const percentage = previousValue ? ((value - previousValue) / previousValue) * 100 : 0;
  const isPositive = percentage >= 0;

  const colorClasses = {
    blue: "bg-blue-500/10 text-blue-600 dark:text-blue-400",
    green: "bg-green-500/10 text-green-600 dark:text-green-400",
    red: "bg-red-500/10 text-red-600 dark:text-red-400",
    purple: "bg-purple-500/10 text-purple-600 dark:text-purple-400",
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.02 }}
      className="relative overflow-hidden"
    >
      <Card className="border-0 shadow-sm transition-shadow hover:shadow-md">
        <CardContent className="p-6">
          {loading ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-3 w-20" />
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-muted-foreground">{title}</p>
                <div className={cn("rounded-lg p-2", colorClasses[color])}>
                  <Icon className="h-5 w-5" />
                </div>
              </div>
              <div className="mt-3">
                <p className="text-2xl font-bold">
                  {isCurrency ? formatCurrency(value) : value.toLocaleString("he-IL")}
                </p>
                {previousValue !== undefined && (
                  <div className="mt-1 flex items-center gap-1 text-sm">
                    {isPositive ? (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    )}
                    <span className={cn(isPositive ? "text-green-600" : "text-red-600")}>
                      {Math.abs(percentage).toFixed(1)}%
                    </span>
                    <span className="text-muted-foreground">מהחודש הקודם</span>
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );

}

// Separate loading component to avoid conditional hooks
function LoadingDashboard({
	selectedPeriod,
	setSelectedPeriod,
	usersPage,
	setUsersPage,
	auditLogsPage,
	setAuditLogsPage,
	auditLogsActionFilter,
	setAuditLogsActionFilter,
}: {
	selectedPeriod: "current" | "previous" | "quarter" | "year";
	setSelectedPeriod: React.Dispatch<
		React.SetStateAction<"current" | "previous" | "quarter" | "year">
	>;
	usersPage: number;
	setUsersPage: React.Dispatch<React.SetStateAction<number>>;
	auditLogsPage: number;
	setAuditLogsPage: React.Dispatch<React.SetStateAction<number>>;
	auditLogsActionFilter: "all" | "create" | "update" | "delete";
	setAuditLogsActionFilter: React.Dispatch<
		React.SetStateAction<"all" | "create" | "update" | "delete">
	>;
}) {
	const refreshData = React.useCallback(() => undefined, []);

	return (
		<div className="flex min-h-screen w-full flex-col gap-6 p-6">
			<EmployerDashboardHeader
				selectedPeriod={selectedPeriod}
				setSelectedPeriod={setSelectedPeriod}
				refreshData={refreshData}
				isLoading={true}
			/>
			<EmployerSystemAlerts isLoading={true} />
			<EmployerMetricsCards isLoading={true} />
			<EmployerDashboardTabs
				visibleTabs={["users", "reports", "logs", "associations"]}
				users={[]}
				isUsersLoading={true}
				usersPage={usersPage}
				setUsersPage={setUsersPage}
				createUser={async () => {}}
				isCreatingUser={false}
				reports={[]}
				isReportsLoading={true}
				logs={[]}
				isLogsLoading={true}
				auditLogsPage={auditLogsPage}
				setAuditLogsPage={setAuditLogsPage}
				auditLogsActionFilter={auditLogsActionFilter}
				setAuditLogsActionFilter={setAuditLogsActionFilter}
				generateReport={refreshData}
				isGeneratingReport={false}
			/>
			<EmployerQuickAccess isLoading={true} />
		</div>
	);

}

// Helper function for formatting currency
function formatCurrency(amount: number) {
  return new Intl.NumberFormat("he-IL", {
    style: "currency",
    currency: "ILS",
  }).format(amount);
}

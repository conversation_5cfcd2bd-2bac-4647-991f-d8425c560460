"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/rtl-components";
import { useState } from "react";

export default function SettingsPage() {
	const [emailNotifications, setEmailNotifications] = useState(true);
	const [autoReports, setAutoReports] = useState(false);

	return (
		<div className="container mx-auto space-y-6 py-6" dir="rtl">
			<div>
				<h1 className="font-bold text-2xl">הגדרות</h1>
				<p className="text-muted-foreground">ניהול העדפות מערכת</p>
			</div>
			<Card>
				<CardHeader>
					<CardTitle>התראות ודוחות</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex items-center justify-between">
						<span>התראות באימייל</span>
						<Switch
							checked={emailNotifications}
							onCheckedChange={setEmailNotifications}
						/>
					</div>
					<div className="flex items-center justify-between">
						<span>שליחת דוחות אוטומטית</span>
						<Switch checked={autoReports} onCheckedChange={setAutoReports} />
					</div>
				</CardContent>
			</Card>
		</div>
	);
}

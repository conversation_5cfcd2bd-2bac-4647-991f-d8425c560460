import { z } from "zod";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";

export const settingRouter = createTRPCRouter({
  getByKey: protectedProcedure
    .input(z.object({ key: z.string() }))
    .query(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      const setting = await ctx.db.setting.findUnique({
        where: {
          tenantId_key: { tenantId: user.tenantId, key: input.key },
        },
      });
      if (!setting) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Setting not found" });
      }
      return { key: setting.key, value: setting.value };
    }),

  update: protectedProcedure
    .input(
      z.object({
        key: z.string(),
        value: z.any(),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true, role: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      if (user.role !== Role.OWNER) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const setting = await ctx.db.setting.upsert({
        where: {
          tenantId_key: { tenantId: user.tenantId, key: input.key },
        },
        update: { value: input.value, description: input.description },
        create: {
          tenantId: user.tenantId,
          key: input.key,
          value: input.value,
          description: input.description,
        },
      });
      return { key: setting.key, value: setting.value };
    }),
});

import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { Role } from "@prisma/client";

export const taxBracketRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(z.object({ year: z.number().optional() }).optional())
    .query(async ({ ctx, input }) => {
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      const where = { tenantId: user.tenantId } as { tenantId: string; year?: number };
      if (input?.year) where.year = input.year;

      const brackets = await ctx.db.taxBracket.findMany({
        where,
        orderBy: { minIncome: "asc" },
      });
      return { taxBrackets: brackets };
    }),

  create: protectedProcedure
    .input(
      z.object({
        year: z.number(),
        minIncome: z.number(),
        maxIncome: z.number(),
        rate: z.number(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.session.user.role !== Role.OWNER) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      return ctx.db.taxBracket.create({
        data: {
          tenantId: user.tenantId,
          year: input.year,
          minIncome: input.minIncome,
          maxIncome: input.maxIncome,
          rate: input.rate,
        },
      });
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        year: z.number().optional(),
        minIncome: z.number().optional(),
        maxIncome: z.number().optional(),
        rate: z.number().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      if (ctx.session.user.role !== Role.OWNER) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      const existing = await ctx.db.taxBracket.findFirst({
        where: { id: input.id, tenantId: user.tenantId },
      });
      if (!existing) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Tax bracket not found" });
      }
      return ctx.db.taxBracket.update({
        where: { id: input.id },
        data: {
          year: input.year ?? existing.year,
          minIncome: input.minIncome ?? existing.minIncome,
          maxIncome: input.maxIncome ?? existing.maxIncome,
          rate: input.rate ?? existing.rate,
        },
      });
    }),

  delete: protectedProcedure
    .input(z.string())
    .mutation(async ({ ctx, input }) => {
      if (ctx.session.user.role !== Role.OWNER) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const user = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });
      if (!user) {
        throw new TRPCError({ code: "NOT_FOUND", message: "User not found" });
      }
      const existing = await ctx.db.taxBracket.findFirst({
        where: { id: input, tenantId: user.tenantId },
      });
      if (!existing) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Tax bracket not found" });
      }
      await ctx.db.taxBracket.delete({ where: { id: input } });
      return { success: true };
    }),
});

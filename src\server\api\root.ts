import { auditLogRouter } from "@/server/api/routers/auditLog";
import { dashboardRouter } from "@/server/api/routers/dashboard";
import { documentSettingsRouter } from "@/server/api/routers/documentSettings";
import { employeeRouter } from "@/server/api/routers/employee";
import { employerRouter } from "@/server/api/routers/employer";
import { integrationRouter } from "@/server/api/routers/integration";
import { referenceRouter } from "@/server/api/routers/reference";
import { reportRouter } from "@/server/api/routers/report";
import { tenantRouter } from "@/server/api/routers/tenant";
import { userRouter } from "@/server/api/routers/user";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";
import { associationsRouter } from "./routers/associations";
import { attendanceAgreementRouter } from "./routers/attendance-agreement";
import { breakRuleRouter } from "./routers/break-rule";
import { deductionComponentRouter } from "./routers/deduction-component";
import { departmentRouter } from "./routers/department";
import { employeeAgreementRouter } from "./routers/employee-agreement";
import { employeeRoleRouter } from "./routers/employee-role";
import { insuranceRatesRouter } from "./routers/insurance-rates";
import { leaveRecordRouter } from "./routers/leaveRecord";
import { locationRouter } from "./routers/location";
import { movementTypeRouter } from "./routers/movement-type";
import { movementRouter } from "./routers/movement";
import { overtimeRuleRouter } from "./routers/overtime-rule";
import { paymentComponentRouter } from "./routers/payment-component";

import { valueComponentRouter } from "./routers/value-component";
import { formulaRouter } from "./routers/formula";

import { salaryAgreementRouter } from "./routers/salary-agreement";
import { salaryTemplateRouter } from "./routers/salary-template";
import { salarySetupRouter } from "./routers/salarySetup";
import { shiftRouter } from "./routers/shift";
import { payslipRouter } from "./routers/payslip";
import { settingRouter } from "./routers/setting";

import { taxBracketRouter } from "./routers/tax-bracket";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
	dashboard: dashboardRouter,
	employer: employerRouter,
	employee: employeeRouter,
	user: userRouter,
	auditLog: auditLogRouter,
	tenant: tenantRouter,
	report: reportRouter,
	integration: integrationRouter,
	documentSettings: documentSettingsRouter,
	associations: associationsRouter,
	attendanceAgreement: attendanceAgreementRouter,
	shift: shiftRouter,
	overtimeRule: overtimeRuleRouter,
	breakRule: breakRuleRouter,
	location: locationRouter,
        movementType: movementTypeRouter,
        movement: movementRouter,
        employeeAgreement: employeeAgreementRouter,
        deductionComponent: deductionComponentRouter,
        paymentComponent: paymentComponentRouter,
        setting: settingRouter,
        insuranceRates: insuranceRatesRouter,

        salaryTemplate: salaryTemplateRouter,

        valueComponent: valueComponentRouter,

        reference: referenceRouter,

        salaryAgreement: salaryAgreementRouter,
        formula: formulaRouter,
        salarySetup: salarySetupRouter,
        leaveRecord: leaveRecordRouter,
        department: departmentRouter,
        employeeRole: employeeRoleRouter,
        taxBracket: taxBracketRouter,
        payslip: payslipRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.user.all();
 *       ^? User[]
 */
export const createCaller = createCallerFactory(appRouter);

import { api } from "@/trpc/react";
import { type EmployeeStatus } from "@/types/employee";
import { useToast } from "@/components/ui/use-toast";

// Get all employees with optional filters
export function useEmployees(filters?: {
  status?: EmployeeStatus;
  department?: string;
  search?: string;
}) {
  return api.employee.getAll.useQuery(filters, {
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

// Get a single employee by ID
export function useEmployee(id: string | null) {
  return api.employee.getById.useQuery(
    { id: id || "" },
    {
      enabled: !!id,
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );
}

// Create a new employee
export function useCreateEmployee() {
  const utils = api.useUtils();
  const { toast } = useToast();

  return api.employee.create.useMutation({
    onSuccess: () => {
      utils.employee.getAll.invalidate();
      toast({
        title: "עובד נוסף בהצלחה",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "שגיאה",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Update an employee
export function useUpdateEmployee() {
  const utils = api.useUtils();
  const { toast } = useToast();

  return api.employee.update.useMutation({
    onSuccess: (data) => {
      utils.employee.getAll.invalidate();
      utils.employee.getById.invalidate({ id: data.id });
      toast({
        title: "פרטי העובד עודכנו",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "שגיאה",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Delete an employee
export function useDeleteEmployee() {
  const utils = api.useUtils();
  const { toast } = useToast();

  return api.employee.delete.useMutation({
    onSuccess: (data) => {
      utils.employee.getAll.invalidate();
      toast({
        title: "העובד הוסר בהצלחה",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "שגיאה",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Get all departments for filtering
export function useDepartments() {
  return api.employee.getDepartments.useQuery(undefined, {
    staleTime: 1000 * 60 * 60, // 1 hour
  });
}

// Get all department objects with search
export function useDepartmentObjects(search?: string) {
  return api.employee.getAllDepartments.useQuery(
    { search },
    {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );
}

// Get a single department by ID
export function useDepartment(id: string | null) {
  return api.employee.getDepartmentById.useQuery(
    { id: id || "" },
    {
      enabled: !!id,
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );
}

// Create a new department
export function useCreateDepartment() {
  const utils = api.useUtils();
  const { toast } = useToast();

  return api.employee.createDepartment.useMutation({
    onSuccess: () => {
      utils.employee.getAllDepartments.invalidate();
      toast({
        title: "מחלקה נוספה בהצלחה",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "שגיאה",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Update a department
export function useUpdateDepartment() {
  const utils = api.useUtils();
  const { toast } = useToast();

  return api.employee.updateDepartment.useMutation({
    onSuccess: (data) => {
      utils.employee.getAllDepartments.invalidate();
      utils.employee.getDepartmentById.invalidate({ id: data.id });
      toast({
        title: "פרטי המחלקה עודכנו",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "שגיאה",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Delete a department
export function useDeleteDepartment() {
  const utils = api.useUtils();
  const { toast } = useToast();

  return api.employee.deleteDepartment.useMutation({
    onSuccess: (data) => {
      utils.employee.getAllDepartments.invalidate();
      toast({
        title: "המחלקה הוסרה בהצלחה",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "שגיאה",
        description: error.message,
        variant: "destructive",
      });
    },
  });
} 
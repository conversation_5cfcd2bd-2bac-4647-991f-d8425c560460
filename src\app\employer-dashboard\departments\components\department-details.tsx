"use client";

import { useState } from "react";
import { useMasterDetail } from "../../layout";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Users, Building, Edit, Trash2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { useDepartment, useEmployees } from "@/hooks/employee-hooks";
import { Skeleton } from "@/components/ui/skeleton";
import { DepartmentForm } from "./department-form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

export function DepartmentDetails() {
  const { selectedItemId } = useMasterDetail();
  const [activeTab, setActiveTab] = useState("details");
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  
  // Fetch department data from API
  const { data: department, isLoading, error } = useDepartment(selectedItemId);
  
  // Fetch employees in this department
  const { data: employeesData } = useEmployees({ 
    department: department?.name
  });
  
  const employees = employeesData?.items || [];

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6 animate-in fade-in duration-300">
        <div className="flex flex-col items-center text-center">
          <Skeleton className="h-24 w-24 rounded-md mb-4" />
          <Skeleton className="h-6 w-40 mb-2" />
          <Skeleton className="h-4 w-32 mb-4" />
        </div>
        
        <div className="flex justify-center gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-9 w-24" />
        </div>
        
        <Separator />
        
        <div>
          <Skeleton className="h-10 w-full mb-6" />
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !department) {
    return (
      <div className="flex h-[calc(100vh-16rem)] flex-col items-center justify-center text-center space-y-4">
        <div className="rounded-full bg-red-100 p-3">
          <Building className="h-6 w-6 text-red-600" />
        </div>
        <h3 className="text-lg font-semibold">שגיאה בטעינת נתונים</h3>
        <p className="text-sm text-muted-foreground">לא ניתן לטעון את פרטי המחלקה. אנא נסה שוב.</p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
          className="mt-2"
        >
          רענן
        </Button>
      </div>
    );
  }

  const handleEdit = () => {
    setIsEditFormOpen(true);
  };

  return (
    <>
      <div className="space-y-6 animate-in fade-in duration-300">
        {/* Department Header */}
        <div className="flex flex-col items-center text-center">
          <Avatar className="h-24 w-24 mb-4 bg-primary/10">
            <AvatarFallback className="text-xl text-primary">
              <Building className="h-12 w-12" />
            </AvatarFallback>
          </Avatar>
          <h2 className="text-xl font-bold">{department.name}</h2>
          <p className="text-muted-foreground">{department.employeeCount} עובדים</p>
        </div>

        <div className="flex justify-center gap-2">
          <Button variant="outline" size="sm" className="flex gap-1" onClick={handleEdit}>
            <Edit className="h-4 w-4" />
            <span>עריכה</span>
          </Button>
          <Button variant="outline" size="sm" className="flex gap-1 text-red-600 hover:text-red-700 hover:bg-red-50">
            <Trash2 className="h-4 w-4" />
            <span>מחיקה</span>
          </Button>
        </div>

        <Separator />

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="details">פרטים</TabsTrigger>
            <TabsTrigger value="employees">עובדים</TabsTrigger>
          </TabsList>
          
          <TabsContent value="details" className="space-y-4 py-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">תיאור</h3>
                <p>{department.description || "אין תיאור"}</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">מנהל</h3>
                <div className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>{department.manager?.[0] || "M"}</AvatarFallback>
                  </Avatar>
                  <span>{department.manager || "לא צוין"}</span>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">סטטיסטיקה</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="rounded-md border p-3 text-center">
                    <p className="text-2xl font-bold">{department.employeeCount}</p>
                    <p className="text-sm text-muted-foreground">עובדים</p>
                  </div>
                  <div className="rounded-md border p-3 text-center">
                    <p className="text-2xl font-bold">₪ 15,000</p>
                    <p className="text-sm text-muted-foreground">שכר ממוצע</p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="employees" className="space-y-4 py-4">
            {employees.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-right">שם</TableHead>
                      <TableHead className="text-right">תפקיד</TableHead>
                      <TableHead className="text-right">סטטוס</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {employees.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell className="font-medium">{employee.name}</TableCell>
                        <TableCell>{employee.position}</TableCell>
                        <TableCell>
                          <Badge
                            variant={employee.status === "פעיל" ? "default" : "outline"}
                            className={
                              employee.status === "פעיל"
                                ? "bg-green-100 text-green-800 hover:bg-green-100/80"
                                : "border-yellow-500 text-yellow-700"
                            }
                          >
                            {employee.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="flex h-40 flex-col items-center justify-center text-center space-y-2 rounded-md border">
                <Users className="h-10 w-10 text-muted-foreground" />
                <h3 className="font-medium">אין עובדים במחלקה זו</h3>
                <p className="text-sm text-muted-foreground">הוסף עובדים למחלקה זו דרך מסך העובדים</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Edit Department Form */}
      <DepartmentForm
        isOpen={isEditFormOpen}
        onClose={() => setIsEditFormOpen(false)}
        initialData={department}
        isEdit={true}
      />
    </>
  );
} 
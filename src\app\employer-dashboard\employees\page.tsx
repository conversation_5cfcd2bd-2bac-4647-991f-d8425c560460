"use client";

import { useState, useEffect } from "react";
import { useMasterDetail } from "../layout";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { EmployeeDetails } from "./components/employee-details";
import { EmployeeForm } from "./components/employee-form";
import { useEmployees, useDepartments } from "@/hooks/employee-hooks";
import { Skeleton } from "@/components/ui/skeleton";

export default function EmployeesPage() {
  const { selectedItemId, setSelectedItemId, setIsDetailOpen, setDetailTitle } = useMasterDetail();
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [isEmployeeFormOpen, setIsEmployeeFormOpen] = useState(false);

  // Debounce search query to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch employees from API
  const { data: employeesData, isLoading, error } = useEmployees({
    search: debouncedSearch,
  });

  const employees = employeesData?.items || [];

  // When employee is selected, update the detail panel title
  useEffect(() => {
    if (selectedItemId) {
      const employee = employees.find(emp => emp.id === selectedItemId);
      if (employee) {
        setDetailTitle(`פרטי עובד: ${employee.name}`);
      }
    }
  }, [selectedItemId, employees, setDetailTitle]);

  // Handle employee selection
  const handleSelectEmployee = (employeeId: string) => {
    setSelectedItemId(employeeId);
    setIsDetailOpen(true);
  };

  // Open form to add new employee
  const handleAddEmployee = () => {
    setIsEmployeeFormOpen(true);
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-2xl font-bold">עובדים</h1>
            <p className="text-muted-foreground">ניהול עובדים ופרטי תעסוקה</p>
          </div>
          <Button onClick={handleAddEmployee} className="self-start md:self-auto">
            <PlusCircle className="ml-2 h-4 w-4" />
            עובד חדש
          </Button>
        </div>

        <div className="relative">
          <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="חיפוש עובדים..."
            className="pr-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">שם</TableHead>
                <TableHead className="text-right">תפקיד</TableHead>
                <TableHead className="text-right">מחלקה</TableHead>
                <TableHead className="text-right">סטטוס</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Loading skeleton
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                    <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  </TableRow>
                ))
              ) : error ? (
                // Error state
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center text-red-500">
                    שגיאה בטעינת נתונים. אנא נסה שוב מאוחר יותר.
                  </TableCell>
                </TableRow>
              ) : employees.length === 0 ? (
                // Empty state
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    {debouncedSearch ? "לא נמצאו עובדים מתאימים" : "אין עובדים להצגה"}
                  </TableCell>
                </TableRow>
              ) : (
                // Employees list
                employees.map((employee) => (
                  <TableRow
                    key={employee.id}
                    className={`cursor-pointer hover:bg-muted/50 ${
                      selectedItemId === employee.id ? "bg-muted" : ""
                    }`}
                    onClick={() => handleSelectEmployee(employee.id)}
                  >
                    <TableCell className="font-medium">{employee.name}</TableCell>
                    <TableCell>{employee.position}</TableCell>
                    <TableCell>{employee.department}</TableCell>
                    <TableCell>
                      <Badge
                        variant={employee.status === "פעיל" ? "default" : "outline"}
                        className={
                          employee.status === "פעיל"
                            ? "bg-green-100 text-green-800 hover:bg-green-100/80"
                            : "border-yellow-500 text-yellow-700"
                        }
                      >
                        {employee.status}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Hidden component to render in the detail panel */}
      {selectedItemId && <EmployeeDetails />}
      
      {/* Employee form dialog */}
      <EmployeeForm
        isOpen={isEmployeeFormOpen}
        onClose={() => setIsEmployeeFormOpen(false)}
      />
    </>
  );
} 
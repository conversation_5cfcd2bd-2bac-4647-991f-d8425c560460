"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCreateEmployee, useUpdateEmployee, useDepartments } from "@/hooks/employee-hooks";
import { type EmployeeStatus } from "@/types/employee";
import { createEmployeeSchema } from "@/schema/employee-shared";

// Infer the schema for form typing
type EmployeeFormValues = z.infer<typeof createEmployeeSchema>;

interface EmployeeFormProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Partial<EmployeeFormValues> & { id?: string };
  isEdit?: boolean;
}

export function EmployeeForm({
  isOpen,
  onClose,
  initialData,
  isEdit = false,
}: EmployeeFormProps) {
  const { data: departments = [] } = useDepartments();
  const createEmployee = useCreateEmployee();
  const updateEmployee = useUpdateEmployee();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form with validation schema
  const form = useForm<EmployeeFormValues>({
    resolver: zodResolver(createEmployeeSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      position: initialData?.position || "",
      department: initialData?.department || "",
      startDate: initialData?.startDate || new Date().toLocaleDateString("he-IL"),
      status: (initialData?.status as EmployeeStatus) || "פעיל",
      salary: initialData?.salary || "₪ 0",
      manager: initialData?.manager || "",
      location: initialData?.location || "",
      employerId: initialData?.employerId || "",
    },
  });

  // Form submission handler
  const onSubmit = async (values: EmployeeFormValues) => {
    setIsSubmitting(true);
    try {
      if (isEdit && initialData?.id) {
        await updateEmployee.mutateAsync({
          ...values,
          id: initialData.id,
        });
      } else {
        await createEmployee.mutateAsync(values);
      }
      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEdit ? "עריכת עובד" : "הוספת עובד חדש"}</DialogTitle>
          <DialogDescription>
            {isEdit ? "ערוך את פרטי העובד" : "הזן את פרטי העובד החדש"}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>שם מלא</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="ישראל ישראלי" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>דוא"ל</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="<EMAIL>" type="email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>טלפון</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="052-1234567" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="position"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>תפקיד</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="מנהל מחלקה" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>מחלקה</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="בחר מחלקה" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {departments.map((department) => (
                          <SelectItem key={department} value={department}>
                            {department}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>תאריך תחילת עבודה</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="DD/MM/YYYY" />
                    </FormControl>
                    <FormDescription>פורמט: DD/MM/YYYY</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>סטטוס</FormLabel>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="בחר סטטוס" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="פעיל">פעיל</SelectItem>
                        <SelectItem value="חופשה">חופשה</SelectItem>
                        <SelectItem value="מושעה">מושעה</SelectItem>
                        <SelectItem value="לא פעיל">לא פעיל</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="salary"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>שכר חודשי</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="₪ 10,000" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="manager"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>מנהל ישיר</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="שם המנהל" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>מיקום</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="תל אביב" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button 
                type="submit" 
                disabled={isSubmitting || createEmployee.isPending || updateEmployee.isPending}
              >
                {isSubmitting ? "שומר..." : isEdit ? "עדכן" : "הוסף"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
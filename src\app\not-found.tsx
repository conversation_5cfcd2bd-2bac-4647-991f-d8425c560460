import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-sky-100 via-indigo-50 to-purple-100">
      {/* Simple header without client-side dependencies */}
      <header className="absolute top-0 right-0 left-0 z-50 border-white/10 border-b bg-white/20 backdrop-blur-md">
        <div className="container mx-auto px-4">
          <div className="flex h-20 items-center justify-between">
            {/* Logo */}
            <Link
              href="/"
              className="flex items-center space-x-2 rtl:space-x-reverse"
            >
              <div className="relative flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-blue-400">
                <span className="font-bold text-white text-xl">S</span>
              </div>
              <span className="font-bold text-navy-800 text-xl">ש<PERSON>ר מערכות</span>
            </Link>

            {/* Simple navigation */}
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Link
                href="/login"
                className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
              >
                התחברות
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1 flex-col items-center justify-center text-center gap-6 p-6">
        <h1 className="text-6xl font-bold text-navy-700">404</h1>
        <p className="text-xl text-navy-600">הדף המבוקש לא נמצא</p>
        <Button asChild>
          <Link href="/">חזרה לדף הבית</Link>
        </Button>
      </div>
    </div>
  );
}

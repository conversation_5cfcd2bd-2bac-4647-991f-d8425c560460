import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { paymentComponentRouter } = await import('../payment-component');
const createCaller = createCallerFactory(paymentComponentRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('paymentComponentRouter.create', () => {
  it('rejects duplicate code', async () => {
    const dbMock = {
      paymentComponent: {
        findFirst: vi.fn().mockResolvedValue({ id: 'p1' }),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock });
    await expect(
      caller.create({ code: 'C1', name: 'p', paymentType: 'OTHER', affectsPension: false, isOneTime: false, isActive: true })
    ).rejects.toBeInstanceOf(Error);
  });

  it('resolves tenant from db', async () => {
    const dbMock = {
      paymentComponent: {
        findFirst: vi.fn().mockResolvedValue(null),
        create: vi.fn().mockResolvedValue({ id: 'p1', code: 'C1', name: 'p', tenantId: 't1' }),
      },
      user: {
        findUnique: vi.fn().mockResolvedValue({ tenantId: 't1' }),
      },
      auditLog: { create: vi.fn().mockResolvedValue({ id: 'a1' }) },
      auditLogValues: { createMany: vi.fn().mockResolvedValue({}) },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', tenantId: 't1' } },
    });
    await caller.create({
      code: 'C1',
      name: 'p',
      paymentType: 'OTHER',
      affectsPension: false,
      isOneTime: false,
      isActive: true,
    });
    expect(dbMock.user.findUnique).toHaveBeenCalledWith({
      where: { id: 'u1' },
      select: { tenantId: true },
    });
    expect(dbMock.paymentComponent.create).toHaveBeenCalledWith({
      data: {
        code: 'C1',
        name: 'p',
        paymentType: 'OTHER',
        affectsPension: false,
        isOneTime: false,
        isActive: true,
        tenantId: 't1',
      },
    });
  });
});

describe('paymentComponentRouter.getUsageStats', () => {
  it('aggregates payslip item stats', async () => {
    const { Prisma } = await import('@prisma/client');
    const dbMock = {
      paymentComponent: {
        findMany: vi.fn().mockResolvedValue([
          { id: 'p1', code: 'c1', name: 'A', tenantId: 't1', _count: { payslipItems: 3 } },
        ]),
      },
      payslipItem: {
        findMany: vi.fn().mockResolvedValue([
          { paymentComponentId: 'p1', amount: new Prisma.Decimal(10), payslip: { employeeId: 'e1', year: 2024, month: 5 } },
          { paymentComponentId: 'p1', amount: new Prisma.Decimal(20), payslip: { employeeId: 'e2', year: 2024, month: 6 } },
        ]),
      },
    } as any;
    const caller = createCaller({ ...baseCtx, db: dbMock });
    const res = await caller.getUsageStats();
    expect(res[0].activeEmployees).toBe(2);
    expect(res[0].totalAmount).toBe(30);
    expect(res[0].lastUsed).toEqual(new Date(2024, 5, 1));

  });
});

import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import {
  type EmployeeStatus as EmployeeStatusType,
  employeeSchema,
  createEmployeeSchema,
  updateEmployeeSchema,
  employeeFilterSchema,
  departmentSchema
} from "@/schema/employee-shared";

// Re-export the EmployeeStatus type for shared usage
export type { EmployeeStatus } from "@/types/employee";

// Schema for creating a new department
export const createDepartmentSchema = departmentSchema
  .omit({ id: true, employeeCount: true })
  .extend({
    name: z.string().min(2).max(100),
  });

// Schema for updating a department
export const updateDepartmentSchema = createDepartmentSchema
  .partial()
  .extend({ id: z.string() });

export const employeeRouter = createTRPCRouter({
  // Get all employees with filters
  getAll: protectedProcedure
    .input(employeeFilterSchema.optional())
    .query(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Mock data
      const employees = [
        { 
          id: "1", 
          name: "שירה כהן", 
          email: "<EMAIL>",
          phone: "052-1234567",
          position: "מנהלת חשבונות", 
          department: "כספים", 
          startDate: "01/01/2020",
          status: "פעיל" as const,
          salary: "₪ 12,500",
          manager: "יובל מנדלסון",
          location: "תל אביב",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "2", 
          name: "דוד לוי", 
          email: "<EMAIL>",
          phone: "054-7654321",
          position: "מפתח תוכנה", 
          department: "הנדסה", 
          startDate: "15/03/2021",
          status: "פעיל" as const,
          salary: "₪ 18,000",
          manager: "מיכאל ברק",
          location: "רמת גן",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "3", 
          name: "מיכל רוזן", 
          email: "<EMAIL>",
          phone: "050-9876543",
          position: "מנהלת משאבי אנוש", 
          department: "משאבי אנוש", 
          startDate: "01/06/2019",
          status: "פעיל" as const,
          salary: "₪ 15,000",
          manager: "יובל מנדלסון",
          location: "תל אביב",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "4", 
          name: "יוסי אברהם", 
          email: "<EMAIL>",
          phone: "053-5555555",
          position: "יועץ משפטי", 
          department: "משפטית", 
          startDate: "01/02/2018",
          status: "חופשה" as const,
          salary: "₪ 22,000",
          manager: "שרה לוין",
          location: "ירושלים",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "5", 
          name: "תמר פרץ", 
          email: "<EMAIL>",
          phone: "052-3333333",
          position: "רואת חשבון", 
          department: "כספים", 
          startDate: "15/05/2022",
          status: "פעיל" as const,
          salary: "₪ 14,500",
          manager: "שירה כהן",
          location: "תל אביב",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "6", 
          name: "עומר שלום", 
          email: "<EMAIL>",
          phone: "054-1112222",
          position: "מנהל פרויקטים", 
          department: "תפעול", 
          startDate: "01/08/2021",
          status: "פעיל" as const,
          salary: "₪ 16,500",
          manager: "ערן דרור",
          location: "חיפה",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "7", 
          name: "אביב ישראלי", 
          email: "<EMAIL>",
          phone: "050-4444444",
          position: "מהנדס QA", 
          department: "הנדסה", 
          startDate: "01/11/2020",
          status: "פעיל" as const,
          salary: "₪ 15,500",
          manager: "מיכאל ברק",
          location: "רמת גן",
          employerId: ctx.session.user.employerId || "default"
        },
        { 
          id: "8", 
          name: "נועה שמעוני", 
          email: "<EMAIL>",
          phone: "052-7777777",
          position: "גרפיקאית", 
          department: "שיווק", 
          startDate: "15/03/2022",
          status: "פעיל" as const,
          salary: "₪ 11,000",
          manager: "ליאת ברמן",
          location: "תל אביב",
          employerId: ctx.session.user.employerId || "default"
        }
      ];

      // Filter data based on input
      let filteredEmployees = employees.filter(emp => 
        emp.employerId === ctx.session.user.employerId
      );

      if (input?.status) {
        filteredEmployees = filteredEmployees.filter(emp => 
          emp.status === input.status
        );
      }

      if (input?.department) {
        filteredEmployees = filteredEmployees.filter(emp => 
          emp.department === input.department
        );
      }

      if (input?.search) {
        const searchLower = input.search.toLowerCase();
        filteredEmployees = filteredEmployees.filter(emp => 
          emp.name.toLowerCase().includes(searchLower) ||
          emp.position.toLowerCase().includes(searchLower) ||
          emp.department.toLowerCase().includes(searchLower) ||
          emp.email.toLowerCase().includes(searchLower)
        );
      }

      return {
        items: filteredEmployees,
        nextCursor: null,
      };
    }),

  // Get employee by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Mock data - same as above but finding a specific employee
      const employees = [
        { 
          id: "1", 
          name: "שירה כהן", 
          email: "<EMAIL>",
          phone: "052-1234567",
          position: "מנהלת חשבונות", 
          department: "כספים", 
          startDate: "01/01/2020",
          status: "פעיל" as const,
          salary: "₪ 12,500",
          manager: "יובל מנדלסון",
          location: "תל אביב",
          employerId: ctx.session.user.employerId || "default"
        },
        // ... other employees
      ];

      const employee = employees.find(emp => 
        emp.id === input.id && emp.employerId === ctx.session.user.employerId
      );

      if (!employee) {
        throw new Error("Employee not found");
      }

      return employee;
    }),

  // Create a new employee
  create: protectedProcedure
    .input(createEmployeeSchema)
    .mutation(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // In a real implementation, we would insert into the database
      // For now, just return a mock response
      return {
        id: `new-${Date.now()}`,
        ...input,
        employerId: ctx.session.user.employerId || "default",
      };
    }),
  // Update an employee
  update: protectedProcedure
    .input(updateEmployeeSchema)
    .mutation(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // In a real implementation, we would update the database
      // For now, just return a mock response
      return {
        ...input,
        employerId: ctx.session.user.employerId || "default",
      };
    }),

  // Delete an employee
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // In a real implementation, we would delete from the database
      // For now, just return a mock response
      return { success: true, id: input.id };
    }),

  // Get departments (for filtering)
  getDepartments: protectedProcedure
    .query(async ({ ctx }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Mock department data
      return [
        "כספים",
        "הנדסה",
        "משאבי אנוש",
        "משפטית",
        "תפעול",
        "שיווק"
      ];
    }),

  // Get all department objects
  getAllDepartments: protectedProcedure
    .input(z.object({
      search: z.string().optional(),
    }).optional())
    .query(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Mock department data
      const departments = [
        {
          id: "1",
          name: "כספים",
          description: "ניהול תקציב וחשבונות החברה",
          employeeCount: 8,
          manager: "שירה כהן",
          employerId: ctx.session.user.employerId || "default",
        },
        {
          id: "2",
          name: "הנדסה",
          description: "פיתוח ותחזוקת מערכות",
          employeeCount: 15,
          manager: "מיכאל ברק",
          employerId: ctx.session.user.employerId || "default",
        },
        {
          id: "3",
          name: "משאבי אנוש",
          description: "גיוס, הכשרה וניהול עובדים",
          employeeCount: 5,
          manager: "מיכל רוזן",
          employerId: ctx.session.user.employerId || "default",
        },
        {
          id: "4",
          name: "משפטית",
          description: "טיפול בנושאים משפטיים וחוזים",
          employeeCount: 3,
          manager: "יוסי אברהם",
          employerId: ctx.session.user.employerId || "default",
        },
        {
          id: "5",
          name: "תפעול",
          description: "ניהול התפעול השוטף",
          employeeCount: 10,
          manager: "ערן דרור",
          employerId: ctx.session.user.employerId || "default",
        },
        {
          id: "6",
          name: "שיווק",
          description: "קידום המוצר וניהול מותג",
          employeeCount: 7,
          manager: "ליאת ברמן",
          employerId: ctx.session.user.employerId || "default",
        },
      ];

      // Filter departments by employer
      let filteredDepartments = departments.filter(
        (dept) => dept.employerId === ctx.session.user.employerId
      );

      // Filter by search term if provided
      if (input?.search) {
        const searchLower = input.search.toLowerCase();
        filteredDepartments = filteredDepartments.filter(
          (dept) =>
            dept.name.toLowerCase().includes(searchLower) ||
            (dept.description?.toLowerCase().includes(searchLower) ?? false) ||
            (dept.manager?.toLowerCase().includes(searchLower) ?? false)
        );
      }

      return {
        items: filteredDepartments,
      };
    }),

  // Get department by ID
  getDepartmentById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Mock department data
      const departments = [
        {
          id: "1",
          name: "כספים",
          description: "ניהול תקציב וחשבונות החברה",
          employeeCount: 8,
          manager: "שירה כהן",
          employerId: ctx.session.user.employerId || "default",
        },
        // ... other departments
      ];

      const department = departments.find(
        (dept) => dept.id === input.id && dept.employerId === ctx.session.user.employerId
      );

      if (!department) {
        throw new Error("Department not found");
      }

      return department;
    }),

  // Create a new department
  createDepartment: protectedProcedure
    .input(createDepartmentSchema)
    .mutation(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      // Mock what would happen in a real database
      const result = {
        id: `dept-${Date.now()}`,
        name: input.name,
        description: input.description || "",
        manager: input.manager || "",
        employeeCount: 0,
        employerId: ctx.session.user.employerId || "default",
      };
      
      return result;
    }),

  // Update a department
  updateDepartment: protectedProcedure
    .input(updateDepartmentSchema)
    .mutation(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // In a real implementation, we would update the database
      // For now, just return a mock response
      const { id, ...rest } = input;
      return {
        ...rest,
        id,
        employeeCount: 0, // In real implementation, this would be fetched from DB
        employerId: ctx.session.user.employerId || "default",
      };
    }),

  // Delete a department
  deleteDepartment: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Simulate database delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // In a real implementation, we would delete from the database
      // For now, just return a mock response
      return { success: true, id: input.id };
    }),

  // Get all Form101 records for a given year
  getAllForm101: protectedProcedure
    .input(z.object({
      year: z.number().int().positive(),
    }))
    .query(async ({ ctx, input }) => {
      // Ensure tenant context exists
      if (!ctx.session?.user?.tenantId) {
        throw new Error("Unauthorized access");
      }

      // Get all form101 records with employee data for a specific year
      const form101Records = await ctx.db.form101.findMany({
        where: {
          tenantId: ctx.session.user.tenantId,
          taxYear: input.year,
          employee: {
            employerId: ctx.session.user.employerId
          }
        },
        include: {
          employee: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              nationalId: true,
              EmployeeContact: {
                select: {
                  email: true,
                  primaryPhone: true,
                  mobilePhone: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Transform the data to match the expected interface in the frontend
      return form101Records.map(record => ({
        ...record,
        employee: {
          id: record.employee.id,
          firstName: record.employee.firstName,
          lastName: record.employee.lastName,
          nationalId: record.employee.nationalId,
          email: record.employee.EmployeeContact?.email || null,
          phone: record.employee.EmployeeContact?.primaryPhone || record.employee.EmployeeContact?.mobilePhone || null
        }
      }));
    }),
});

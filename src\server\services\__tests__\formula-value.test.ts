import type { PrismaClient } from "@prisma/client";
import { describe, expect, it } from "vitest";
import { calculateFormulaValue } from "../payslip";

const dummyContext = {
	baseSalary: 1000,
	hourlyRate: 50,
	workDays: 20,
	workHours: 160,
	valueMap: new Map<string, unknown>(),
	employee: {},
	db: {} as PrismaClient,
	month: 1,
	year: 2024,
};

describe("calculateFormulaValue", () => {
	it("evaluates a simple expression", async () => {
		const result = await calculateFormulaValue(
			{ formulaCode: "{baseSalary} * 0.1" },
			dummyContext,
		);
		expect(result).toBe(100);
	});

	it("handles valueMap variables", async () => {
		const ctx = { ...dummyContext, valueMap: new Map([["OVERTIME", {}]]) };
		const result = await calculateFormulaValue(
			{ formulaCode: "{OVERTIME} * 2" },
			ctx,
		);
		expect(result).toBe(200);
	});

	it("throws on invalid expression", async () => {
		await expect(
			calculateFormulaValue({ formulaCode: "invalid$$" }, dummyContext),
		).rejects.toThrow();
	});

	it("throws when variable missing", async () => {
		await expect(
			calculateFormulaValue({ formulaCode: "{missing} + 1" }, dummyContext),
		).rejects.toThrow();
	});
});

"use client";

import { redirect } from "next/navigation";
import { useSession, signOut } from "next-auth/react";
import { type ReactNode, useState, createContext, useContext, useEffect } from "react";
import { Sidebar } from "./components/sidebar";
import { Header } from "./components/header";
import { cn } from "@/lib/utils";
import { ChevronRight, ChevronLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { AnimatePresence, motion } from "framer-motion";

interface EmployerDashboardLayoutProps {
	children: ReactNode;
}

// Context for managing selected item across the dashboard
interface MasterDetailContextType {
	selectedItemId: string | null;
	setSelectedItemId: (id: string | null) => void;
	isDetailOpen: boolean;
	setIsDetailOpen: (open: boolean) => void;
	detailTitle: string;
	setDetailTitle: (title: string) => void;
}

const MasterDetailContext = createContext<MasterDetailContextType | undefined>(undefined);

export const useMasterDetail = () => {
	const context = useContext(MasterDetailContext);
	if (!context) {
		throw new Error("useMasterDetail must be used within MasterDetailProvider");
	}
	return context;
};

export default function EmployerDashboardLayout({
	children,
}: EmployerDashboardLayoutProps) {
	const { data: session, status } = useSession();
	const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
	const [isDetailOpen, setIsDetailOpen] = useState(false);
	const [detailTitle, setDetailTitle] = useState("פרטים");
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

	// Close detail panel when switching routes
	useEffect(() => {
		setIsDetailOpen(false);
		setSelectedItemId(null);
	}, []);

	// Handle session loading state
	if (status === "loading") {
		return (
			<div dir="rtl" className="flex min-h-screen items-center justify-center bg-background">
				<div className="flex flex-col items-center gap-4">
					<div className="h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
					<div className="text-lg font-medium">טוען...</div>
				</div>
			</div>
		);
	}

	// Redirect if not authenticated
	if (!session) {
		redirect("/login");
	}

	// Get user info from session
	const userName = session?.user?.name || "משתמש";
	const employerName = session?.user?.employerName || "מעסיק";
	const userEmail = session?.user?.email || "";
	const avatarFallback = userName.charAt(0).toUpperCase();

	const handleLogout = async () => {
		await signOut({ callbackUrl: "/login" });
	};

	return (
		<div dir="rtl" className="flex min-h-screen flex-col bg-background antialiased">
			<MasterDetailContext.Provider 
				value={{ 
					selectedItemId, 
					setSelectedItemId, 
					isDetailOpen, 
					setIsDetailOpen,
					detailTitle,
					setDetailTitle
				}}
			>
				{/* Header - Fixed at top */}
				<Header />
				
				{/* Mobile sidebar toggle - visible only on mobile */}
				<div className="fixed bottom-4 right-4 z-50 md:hidden">
					<Button
						onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
						size="icon"
						className="h-12 w-12 rounded-full shadow-lg"
					>
						{isMobileMenuOpen ? <ChevronRight className="h-6 w-6" /> : <ChevronLeft className="h-6 w-6" />}
					</Button>
				</div>

				{/* Mobile sidebar - slides in from right */}
				<AnimatePresence>
					{isMobileMenuOpen && (
						<motion.div
							initial={{ x: "100%" }}
							animate={{ x: 0 }}
							exit={{ x: "100%" }}
							transition={{ duration: 0.3, ease: "easeInOut" }}
							className="fixed inset-y-0 right-0 z-40 w-64 bg-background shadow-xl md:hidden"
						>
							<Sidebar 
								employerName={employerName}
								userName={userName}
								userEmail={userEmail}
								avatarFallback={avatarFallback}
								onLogout={handleLogout}
							/>
						</motion.div>
					)}
				</AnimatePresence>

				{/* Main content area */}
				<div className="container flex-1 items-start pt-20 pb-8">
					<div className={cn(
						"grid grid-cols-1 gap-6 transition-all duration-300",
						isDetailOpen 
							? "md:grid-cols-[240px_1fr_450px] lg:grid-cols-[280px_1fr_500px]" 
							: "md:grid-cols-[240px_1fr] lg:grid-cols-[280px_1fr]"
					)}>
						{/* Desktop Sidebar - always visible on larger screens */}
						<aside className="hidden md:block sticky top-20 h-[calc(100vh-8rem)] overflow-y-auto border-l rounded-md">
							<Sidebar 
								employerName={employerName}
								userName={userName}
								userEmail={userEmail}
								avatarFallback={avatarFallback}
								onLogout={handleLogout}
							/>
						</aside>
						
						{/* Master View - Main content */}
						<main className="flex w-full flex-col overflow-hidden rounded-md border p-4 shadow-sm">
							{children}
						</main>
						
						{/* Detail View - Animated slide-in panel */}
						<AnimatePresence>
							{isDetailOpen && (
								<motion.aside
									initial={{ x: 50, opacity: 0 }}
									animate={{ x: 0, opacity: 1 }}
									exit={{ x: 50, opacity: 0 }}
									transition={{ duration: 0.3, ease: "easeInOut" }}
									className="hidden md:block sticky top-20 z-30 h-[calc(100vh-8rem)] overflow-y-auto rounded-md border bg-background/50 p-6 shadow-sm backdrop-blur"
								>
									<div className="flex justify-between items-center mb-6">
										<h2 className="text-xl font-semibold">{detailTitle}</h2>
										<Button
											variant="ghost"
											size="icon"
											onClick={() => {
												setIsDetailOpen(false);
												setSelectedItemId(null);
											}}
											className="text-muted-foreground hover:text-foreground transition-colors"
										>
											<ChevronRight className="h-5 w-5" />
										</Button>
									</div>
									
									<div id="detail-content" className="space-y-4">
										{selectedItemId ? (
											<div className="animate-in fade-in duration-300">
												{/* Detail content will be rendered here by child components */}
											</div>
										) : (
											<div className="text-center py-8 text-muted-foreground">
												<p>בחר פריט כדי להציג פרטים</p>
											</div>
										)}
									</div>
								</motion.aside>
							)}
						</AnimatePresence>
					</div>
				</div>
			</MasterDetailContext.Provider>
		</div>
	);
}


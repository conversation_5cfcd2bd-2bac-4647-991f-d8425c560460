import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { attendanceAgreementRouter } = await import('../attendance-agreement');
const createCaller = createCallerFactory(attendanceAgreementRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('attendanceAgreementRouter.create', () => {
  it('uses tenant from session', async () => {
    const dbMock = {
      attendanceAgreement: { create: vi.fn().mockResolvedValue({ id: 'a1' }), findFirst: vi.fn().mockResolvedValue(null) },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', tenantId: 't1' } },
    });
    await caller.create({
      code: 'C1',
      name: 'Agreement',
      workDaysPerWeek: 5,
      hoursPerDay: 8,
      monthlyHours: 160,
      overtimeThreshold: 8,
    });
    expect(dbMock.attendanceAgreement.create).toHaveBeenCalledWith({
      data: {
        code: 'C1',
        name: 'Agreement',
        description: undefined,
        status: 'ACTIVE',
        workDaysPerWeek: 5,
        hoursPerDay: 8,
        monthlyHours: 160,
        overtimeThreshold: 8,
        nightShiftStart: undefined,
        nightShiftEnd: undefined,
        weekendDays: [5, 6],
        tenantId: 't1',
      },
    });
  });
});

import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { Role } from "@prisma/client";
import type { Prisma } from "@prisma/client";

// Input validation schemas
const valueComponentTypeEnum = z.enum(["VEHICLE", "PHONE", "MEAL", "INTEREST", "OTHER"]);
const taxCalculationEnum = z.enum(["TAX_LIABLE", "TAX_EXEMPT"]);
const socialSecurityEnum = z.enum(["SS_LIABLE", "SS_EXEMPT"]);

const createValueComponentSchema = z.object({
  code: z.string().min(1),
  name: z.string().min(1),
  description: z.string().optional(),
  valueComponentType: valueComponentTypeEnum,
  taxCalculation: taxCalculationEnum.optional(),
  socialSecurityCalculation: socialSecurityEnum.optional(),
  affectsPension: z.boolean().default(false),
  isOneTime: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

const updateValueComponentSchema = createValueComponentSchema.extend({
  id: z.string(),
});

const filtersSchema = z.object({
  search: z.string().optional(),
  valueComponentType: valueComponentTypeEnum.optional(),
  isActive: z.boolean().optional(),
  isOneTime: z.boolean().optional(),
}).optional();

export const valueComponentRouter = createTRPCRouter({
  // Get all value components
  getAll: publicProcedure
    .input(filtersSchema)
    .query(async ({ ctx, input }) => {
      const where: Prisma.ValueComponentWhereInput = { deletedAt: null };

      if (input?.search) {
        where.OR = [
          { name: { contains: input.search, mode: "insensitive" } },
          { code: { contains: input.search, mode: "insensitive" } },
          { description: { contains: input.search, mode: "insensitive" } },
        ];
      }

      if (input?.valueComponentType) {
        where.valueComponentType = input.valueComponentType;
      }

      if (input?.isActive !== undefined) {
        where.isActive = input.isActive;
      }

      if (input?.isOneTime !== undefined) {
        where.isOneTime = input.isOneTime;
      }

      return ctx.db.valueComponent.findMany({
        where,
        orderBy: { name: "asc" },
      });
    }),

  // Get value component by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const component = await ctx.db.valueComponent.findUnique({
        where: { id: input.id },
      });

      if (!component || component.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ערך לא נמצא",
        });
      }

      return component;
    }),

  // Create new value component
  create: protectedProcedure
    .input(createValueComponentSchema)
    .mutation(async ({ ctx, input }) => {
      const existing = await ctx.db.valueComponent.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "קוד רכיב ערך כבר קיים במערכת",
        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;

      return ctx.db.valueComponent.create({
        data: {
          ...input,
          tenantId,
        },
      });
    }),

  // Update value component
  update: protectedProcedure
    .input(updateValueComponentSchema)
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const { id, ...data } = input;

      const existing = await ctx.db.valueComponent.findUnique({
        where: { id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ערך לא נמצא",
        });
      }

      if (data.code !== existing.code) {
        const codeExists = await ctx.db.valueComponent.findFirst({
          where: {
            code: data.code,
            id: { not: id },
            deletedAt: null,
          },
        });

        if (codeExists) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "קוד רכיב ערך כבר קיים במערכת",
          });
        }
      }

      return ctx.db.valueComponent.update({
        where: { id },
        data,
      });
    }),

  // Delete value component (soft delete)
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (
        ctx.session.user.role !== Role.OWNER &&
        ctx.session.user.role !== Role.ADMIN
      ) {
        throw new TRPCError({ code: "FORBIDDEN", message: "Insufficient permissions" });
      }
      const existing = await ctx.db.valueComponent.findUnique({
        where: { id: input.id },
      });

      if (!existing || existing.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ערך לא נמצא",
        });
      }

      return ctx.db.valueComponent.update({
        where: { id: input.id },
        data: {
          deletedAt: new Date(),
        },
      });
    }),

  // Duplicate value component
  duplicate: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const original = await ctx.db.valueComponent.findUnique({
        where: { id: input.id },
      });

      if (!original || original.deletedAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "רכיב ערך לא נמצא",
        });
      }

      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;

      let newCode = `${original.code}_COPY`;
      let counter = 1;

      while (true) {
        const exists = await ctx.db.valueComponent.findFirst({
          where: {
            code: newCode,
            deletedAt: null,
          },
        });

        if (!exists) break;

        counter++;
        newCode = `${original.code}_COPY${counter}`;
      }

      return ctx.db.valueComponent.create({
        data: {
          ...original,
          id: undefined,
          code: newCode,
          name: `${original.name} (עותק)`,
          createdAt: undefined,
          updatedAt: undefined,
          tenantId,
        },
      });
    }),

  // Validate value component
  validate: publicProcedure
    .input(createValueComponentSchema)
    .query(async ({ ctx, input }) => {
      const errors: string[] = [];
      const warnings: string[] = [];

      const existingCode = await ctx.db.valueComponent.findFirst({
        where: {
          code: input.code,
          deletedAt: null,
        },
      });

      if (existingCode) {
        errors.push("קוד רכיב ערך כבר קיים במערכת");
      }

      const similarName = await ctx.db.valueComponent.findFirst({
        where: {
          name: {
            equals: input.name,
            mode: "insensitive",
          },
          deletedAt: null,
        },
      });

      if (similarName) {
        warnings.push("קיים רכיב ערך עם שם דומה");
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    }),

  // Get usage statistics
  getUsageStats: publicProcedure
    .query(async ({ ctx }) => {
      const components = await ctx.db.valueComponent.findMany({
        where: { deletedAt: null },
      });

      return components.map(component => ({
        valueComponentId: component.id,
        valueComponent: component,
        usageCount: 0,
        activeEmployees: 0,
        totalAmount: 0,
        lastUsed: null,
      }));
    }),

  // Get change report
  getChangeReport: publicProcedure
    .input(z.object({
      fromDate: z.date(),
      toDate: z.date(),
    }))
    .query(async ({ ctx, input }) => {
      return {
        fromDate: input.fromDate,
        toDate: input.toDate,
        changes: [],
        summary: {
          totalChanges: 0,
          createdCount: 0,
          updatedCount: 0,
          deletedCount: 0,
        },
      };
    }),

  // Bulk update
  bulkUpdate: publicProcedure
    .input(z.object({
      ids: z.array(z.string()),
      updates: z.object({
        isActive: z.boolean().optional(),
        valueComponentType: valueComponentTypeEnum.optional(),
        taxCalculation: taxCalculationEnum.optional(),
        socialSecurityCalculation: socialSecurityEnum.optional(),
      }),
    }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.db.valueComponent.updateMany({
        where: {
          id: { in: input.ids },
          deletedAt: null,
        },
        data: input.updates,
      });

      return {
        updated: result.count,
      };
    }),

  // Export value components
  export: publicProcedure
    .mutation(async ({ ctx }) => {
      const components = await ctx.db.valueComponent.findMany({
        where: { deletedAt: null },
        orderBy: { name: "asc" },
      });

      return components;
    }),

  // Import value components
  import: protectedProcedure
    .input(z.array(createValueComponentSchema))
    .mutation(async ({ ctx, input }) => {
      const userWithTenant = await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true },
      });

      if (!userWithTenant) {
        throw new TRPCError({ code: "UNAUTHORIZED", message: "User not found" });
      }

      const tenantId = userWithTenant.tenantId;
      let imported = 0;

      for (const component of input) {
        const existing = await ctx.db.valueComponent.findFirst({
          where: {
            code: component.code,
            deletedAt: null,
          },
        });

        if (!existing) {
          await ctx.db.valueComponent.create({
            data: {
              ...component,
              tenantId,
            },
          });
          imported++;
        }
      }

      return {
        imported,
        total: input.length,
      };
    }),
});

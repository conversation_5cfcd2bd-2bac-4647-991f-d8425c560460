import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { referenceRouter } = await import('../reference');
const createCaller = createCallerFactory(referenceRouter);

const baseCtx = { db: {}, logger: undefined, headers: new Headers() } as any;

describe('referenceRouter.lock', () => {
  it('sets locked flag in settings', async () => {
    const dbMock = {
      setting: { upsert: vi.fn() },
    } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', tenantId: 't1' } },
    });
    await caller.lock();
    expect(dbMock.setting.upsert).toHaveBeenCalledWith({
      where: { tenantId_key: { tenantId: 't1', key: 'reference.locked' } },
      create: { tenantId: 't1', key: 'reference.locked', value: { locked: true } },
      update: { value: { locked: true } },
    });
  });
});

"use client";

import { Button } from "@/components/ui/rtl-components";
import { Input } from "@/components/ui/input";
import { useState } from "react";

export default function CompanyPage() {
	const [name, setName] = useState("חברת דוגמה בע" + "מ");
	const [address, setAddress] = useState("רחוב הראשי 1, תל אביב");
	const [taxId, setTaxId] = useState("*********");

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		alert("נשמר בהצלחה");
	};

	return (
		<div className="container mx-auto space-y-6 py-6" dir="rtl">
			<div>
				<h1 className="font-bold text-2xl">פרטי חברה</h1>
				<p className="text-muted-foreground">
					עדכון פרטי החברה והגדרות בסיסיות
				</p>
			</div>
			<form onSubmit={handleSubmit} className="max-w-md space-y-4">
				<div className="space-y-2">
					<label className="block font-medium text-sm" htmlFor="name">
						שם חברה
					</label>
					<Input
						id="name"
						value={name}
						onChange={(e) => setName(e.target.value)}
					/>
				</div>
				<div className="space-y-2">
					<label className="block font-medium text-sm" htmlFor="address">
						כתובת
					</label>
					<Input
						id="address"
						value={address}
						onChange={(e) => setAddress(e.target.value)}
					/>
				</div>
				<div className="space-y-2">
					<label className="block font-medium text-sm" htmlFor="tax">
						תיק ניכויים
					</label>
					<Input
						id="tax"
						value={taxId}
						onChange={(e) => setTaxId(e.target.value)}
					/>
				</div>
				<Button type="submit">שמירה</Button>
			</form>
		</div>
	);
}

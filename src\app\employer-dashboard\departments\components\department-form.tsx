"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useCreateDepartment, useUpdateDepartment } from "@/hooks/employee-hooks";
import { createDepartmentSchema } from "@/server/api/routers/employee";

// Infer the schema for form typing
type DepartmentFormValues = z.infer<typeof createDepartmentSchema>;

interface DepartmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Partial<DepartmentFormValues> & { id?: string };
  isEdit?: boolean;
}

export function DepartmentForm({
  isOpen,
  onClose,
  initialData,
  isEdit = false,
}: DepartmentFormProps) {
  const createDepartment = useCreateDepartment();
  const updateDepartment = useUpdateDepartment();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Define form with validation schema
  const form = useForm<DepartmentFormValues>({
    resolver: zodResolver(createDepartmentSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      manager: initialData?.manager || "",
      employerId: initialData?.employerId || "",
    },
  });

  // Form submission handler
  const onSubmit = async (values: DepartmentFormValues) => {
    setIsSubmitting(true);
    try {
      if (isEdit && initialData?.id) {
        await updateDepartment.mutateAsync({
          ...values,
          id: initialData.id,
        });
      } else {
        await createDepartment.mutateAsync(values);
      }
      onClose();
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEdit ? "עריכת מחלקה" : "הוספת מחלקה חדשה"}</DialogTitle>
          <DialogDescription>
            {isEdit ? "ערוך את פרטי המחלקה" : "הזן את פרטי המחלקה החדשה"}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>שם המחלקה</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="כספים" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>תיאור המחלקה</FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="תיאור תפקידי המחלקה ואחריותה" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="manager"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>מנהל המחלקה</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="שם המנהל" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button 
                type="submit" 
                disabled={isSubmitting || createDepartment.isPending || updateDepartment.isPending}
              >
                {isSubmitting ? "שומר..." : isEdit ? "עדכן" : "הוסף"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
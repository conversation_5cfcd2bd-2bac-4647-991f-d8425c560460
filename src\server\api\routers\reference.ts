import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";

export const referenceRouter = createTRPCRouter({
  lock: protectedProcedure.mutation(async ({ ctx }) => {
    const tenantId = ctx.session.user.tenantId;
    if (!tenantId) {
      throw new Error("Missing tenantId in session");
    }
    await ctx.db.setting.upsert({
      where: { tenantId_key: { tenantId, key: "reference.locked" } },
      create: { tenantId, key: "reference.locked", value: { locked: true } },
      update: { value: { locked: true } },
    });
    return { locked: true };
  }),
});
